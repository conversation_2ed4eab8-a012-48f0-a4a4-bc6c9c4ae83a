/* Custom font definitions - 使用相对路径 */
@font-face {
    font-family: 'Courgette';
    src: url('../assets/fonts/Courgette-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Great Vibes';
    src: url('../assets/fonts/GreatVibes-Regular.ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'ZiXiaoHunGouYu';
    src: url('../assets/fonts/字小魂勾玉行书(商用需授权).ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'ZiXiaoHunSanFen';
    src: url('../assets/fonts/字小魂三分行楷(商用需授权).ttf') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

/* Reset and base styles - Version 3.0 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 防止刷新时白色闪烁 - html根元素背景（与加载遮罩一致的粉色渐变） */
html {
    background: linear-gradient(135deg,
        #fce7f3 0%,
        #fbcfe8 25%,
        #f9a8d4 50%,
        #f472b6 75%,
        #ec4899 100%) !important;
    margin: 0 !important;
    padding: 0 !important;
    min-height: 100vh !important;
}

/* Force author label styling - CRITICAL */
.author-selection span.author-label,
.author-option span.author-label,
span.author-label {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive !important;
    color: white !important;
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.4) !important;
}

body {
    font-family: 'Inter', 'Poppins', 'ZiXiaoHunGouYu', sans-serif;
    background: transparent !important; /* 移除遮罩，让视频背景完全显示 */
    min-height: 100vh;
    overflow-x: hidden;
    position: relative;
    color: #2c3e50;
    line-height: 1.6;
    font-weight: 400;
}

/* 花朵视频背景样式 */
.video-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.video-background video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0; /* 初始透明，加载完成后显示 */
    filter: contrast(1.1) saturate(1.2); /* 保持原始亮度，仅增强对比度和饱和度 */
    transition: opacity 1s ease-in-out; /* 平滑过渡效果 */
}

/* 视频加载完成后显示 */
.video-background video.loaded {
    opacity: 1.0; /* 完全不透明，显示视频原始亮度 */
}

/* 视频加载时的备用背景 */
.video-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        #fce7f3 0%,
        #fbcfe8 25%,
        #f9a8d4 50%,
        #f472b6 75%,
        #ec4899 100%);
    z-index: -1;
    transition: opacity 1s ease-in-out;
}

/* 视频加载完成后隐藏备用背景 */
.video-background.video-loaded::before {
    opacity: 0;
}

/* 加载遮罩样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        #fce7f3 0%,
        #fbcfe8 25%,
        #f9a8d4 50%,
        #f472b6 75%,
        #ec4899 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 1;
    visibility: visible;
    transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
}

.loading-overlay.hidden {
    opacity: 0;
    visibility: hidden;
}

/* 加载动画 */
.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
    opacity: 1;
    visibility: visible;
    display: block;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 10px;
    opacity: 1;
    visibility: visible;
    display: block;
}

.loading-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-size: 1rem;
    text-align: center;
    opacity: 1;
    visibility: visible;
    display: block;
}

/* 加载进度条 */
.loading-progress {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin-top: 20px;
    overflow: hidden;
    opacity: 1;
    visibility: visible;
    display: block;
}

.loading-progress-bar {
    height: 100%;
    background: white;
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
    animation: progressPulse 2s ease-in-out infinite;
    opacity: 1;
    visibility: visible;
    display: block;
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* 星空背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, #fff, transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 90px 40px, #fff, transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
        radial-gradient(2px 2px at 160px 30px, #fff, transparent),
        radial-gradient(1px 1px at 200px 60px, rgba(255,255,255,0.7), transparent),
        radial-gradient(2px 2px at 250px 20px, #fff, transparent),
        radial-gradient(1px 1px at 300px 90px, rgba(255,255,255,0.5), transparent);
    background-repeat: repeat;
    background-size: 350px 150px;
    animation: sparkle 25s linear infinite;
    pointer-events: none;
    opacity: 0.2; /* 调整星空效果的透明度，与花朵背景更好融合 */
    z-index: 1;
}

@keyframes sparkle {
    from { transform: translateX(0); }
    to { transform: translateX(350px); }
}

/* 闪烁星星效果 */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 15px 25px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 85px 45px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 145px 15px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 205px 75px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 265px 35px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 325px 65px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 385px 25px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 445px 85px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 55px 95px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 115px 125px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 175px 55px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 235px 105px, rgba(255,255,255,0.7), transparent),
        radial-gradient(1px 1px at 295px 75px, rgba(255,255,255,0.8), transparent),
        radial-gradient(1px 1px at 355px 115px, rgba(255,255,255,0.6), transparent),
        radial-gradient(1px 1px at 415px 45px, rgba(255,255,255,0.9), transparent),
        radial-gradient(1px 1px at 475px 95px, rgba(255,255,255,0.7), transparent);
    background-repeat: repeat;
    background-size: 500px 140px;
    animation: twinkle 6s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
    opacity: 0.15; /* 调整星空效果的透明度，与花朵背景更好融合 */
}

@keyframes twinkle {
    0%, 100% { opacity: 0.2; }
    12.5% { opacity: 0.9; }
    25% { opacity: 0.4; }
    37.5% { opacity: 1; }
    50% { opacity: 0.3; }
    62.5% { opacity: 0.8; }
    75% { opacity: 0.5; }
    87.5% { opacity: 0.95; }
}

/* 浪漫可点击星星 */
.romantic-stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    pointer-events: none;
}

.romantic-star {
    position: absolute;
    width: 26px;
    height: 26px;
    cursor: pointer;
    pointer-events: all;
    animation: starTwinkleDisappear 6s ease-in-out infinite, starFloat 10s ease-in-out infinite;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* SVG星星带多层密集点填充 */
.romantic-star::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFFF00">.</text></pattern><pattern id="p2" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFFF00">.</text></pattern><pattern id="p3" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFFF00">.</text></pattern><clipPath id="starClipC"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClipC)"><rect width="24" height="24" fill="url(%23p1)"/><rect width="24" height="24" fill="url(%23p2)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23FFD700" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.6)) brightness(1.3);
}

.romantic-star:hover {
    animation-duration: 2s;
}

/* 悬停时保持旋转角度并放大 */
.romantic-star:nth-child(1):hover { transform: rotate(15deg) scale(1.2); }
.romantic-star:nth-child(2):hover { transform: rotate(-22deg) scale(1.2); }
.romantic-star:nth-child(3):hover { transform: rotate(38deg) scale(1.2); }
.romantic-star:nth-child(4):hover { transform: rotate(-45deg) scale(1.2); }
.romantic-star:nth-child(5):hover { transform: rotate(12deg) scale(1.2); }
.romantic-star:nth-child(6):hover { transform: rotate(-33deg) scale(1.2); }
.romantic-star:nth-child(7):hover { transform: rotate(27deg) scale(1.2); }
.romantic-star:nth-child(8):hover { transform: rotate(-18deg) scale(1.2); }
.romantic-star:nth-child(9):hover { transform: rotate(42deg) scale(1.2); }
.romantic-star:nth-child(10):hover { transform: rotate(-8deg) scale(1.2); }
.romantic-star:nth-child(11):hover { transform: rotate(35deg) scale(1.2); }
.romantic-star:nth-child(12):hover { transform: rotate(-28deg) scale(1.2); }
.romantic-star:nth-child(13):hover { transform: rotate(20deg) scale(1.2); }
.romantic-star:nth-child(14):hover { transform: rotate(-40deg) scale(1.2); }
.romantic-star:nth-child(15):hover { transform: rotate(5deg) scale(1.2); }
.romantic-star:nth-child(16):hover { transform: rotate(-15deg) scale(1.2); }

.romantic-star:hover::before {
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8)) brightness(1.5);
}



/* 星星淡入淡出动画 - 确保位置变化完全隐藏 */
@keyframes starTwinkleDisappear {
    0% {
        opacity: 1;
        transform: scale(1);
        filter: brightness(1.3);
    }
    20% {
        opacity: 0.6;
        transform: scale(0.95);
        filter: brightness(1.1);
    }
    40% {
        opacity: 0.1;
        transform: scale(0.9);
        filter: brightness(0.8);
    }
    50% {
        opacity: 0;
        transform: scale(0.85);
        filter: brightness(0.5);
    }
    60% {
        opacity: 0.1;
        transform: scale(0.9);
        filter: brightness(0.8);
    }
    80% {
        opacity: 0.6;
        transform: scale(0.95);
        filter: brightness(1.1);
    }
    100% {
        opacity: 1;
        transform: scale(1);
        filter: brightness(1.3);
    }
}



/* 星星位置随机漂浮动画 - 基础版本 */
@keyframes starFloat {
    0%, 100% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(3px, -2px);
    }
    50% {
        transform: translate(-2px, 3px);
    }
    75% {
        transform: translate(2px, 1px);
    }
}

/* 为每个星星创建带旋转的漂浮动画 */
@keyframes starFloat1 {
    0%, 100% { transform: rotate(15deg) translate(0, 0); }
    25% { transform: rotate(15deg) translate(3px, -2px); }
    50% { transform: rotate(15deg) translate(-2px, 3px); }
    75% { transform: rotate(15deg) translate(2px, 1px); }
}

@keyframes starFloat2 {
    0%, 100% { transform: rotate(-22deg) translate(0, 0); }
    25% { transform: rotate(-22deg) translate(3px, -2px); }
    50% { transform: rotate(-22deg) translate(-2px, 3px); }
    75% { transform: rotate(-22deg) translate(2px, 1px); }
}

@keyframes starFloat3 {
    0%, 100% { transform: rotate(38deg) translate(0, 0); }
    25% { transform: rotate(38deg) translate(3px, -2px); }
    50% { transform: rotate(38deg) translate(-2px, 3px); }
    75% { transform: rotate(38deg) translate(2px, 1px); }
}

@keyframes starFloat4 {
    0%, 100% { transform: rotate(-45deg) translate(0, 0); }
    25% { transform: rotate(-45deg) translate(3px, -2px); }
    50% { transform: rotate(-45deg) translate(-2px, 3px); }
    75% { transform: rotate(-45deg) translate(2px, 1px); }
}

@keyframes starFloat5 {
    0%, 100% { transform: rotate(12deg) translate(0, 0); }
    25% { transform: rotate(12deg) translate(3px, -2px); }
    50% { transform: rotate(12deg) translate(-2px, 3px); }
    75% { transform: rotate(12deg) translate(2px, 1px); }
}

@keyframes starFloat6 {
    0%, 100% { transform: rotate(-33deg) translate(0, 0); }
    25% { transform: rotate(-33deg) translate(3px, -2px); }
    50% { transform: rotate(-33deg) translate(-2px, 3px); }
    75% { transform: rotate(-33deg) translate(2px, 1px); }
}

@keyframes starFloat7 {
    0%, 100% { transform: rotate(27deg) translate(0, 0); }
    25% { transform: rotate(27deg) translate(3px, -2px); }
    50% { transform: rotate(27deg) translate(-2px, 3px); }
    75% { transform: rotate(27deg) translate(2px, 1px); }
}

@keyframes starFloat8 {
    0%, 100% { transform: rotate(-18deg) translate(0, 0); }
    25% { transform: rotate(-18deg) translate(3px, -2px); }
    50% { transform: rotate(-18deg) translate(-2px, 3px); }
    75% { transform: rotate(-18deg) translate(2px, 1px); }
}

@keyframes starFloat9 {
    0%, 100% { transform: rotate(42deg) translate(0, 0); }
    25% { transform: rotate(42deg) translate(3px, -2px); }
    50% { transform: rotate(42deg) translate(-2px, 3px); }
    75% { transform: rotate(42deg) translate(2px, 1px); }
}

@keyframes starFloat10 {
    0%, 100% { transform: rotate(-8deg) translate(0, 0); }
    25% { transform: rotate(-8deg) translate(3px, -2px); }
    50% { transform: rotate(-8deg) translate(-2px, 3px); }
    75% { transform: rotate(-8deg) translate(2px, 1px); }
}

@keyframes starFloat11 {
    0%, 100% { transform: rotate(35deg) translate(0, 0); }
    25% { transform: rotate(35deg) translate(3px, -2px); }
    50% { transform: rotate(35deg) translate(-2px, 3px); }
    75% { transform: rotate(35deg) translate(2px, 1px); }
}

@keyframes starFloat12 {
    0%, 100% { transform: rotate(-28deg) translate(0, 0); }
    25% { transform: rotate(-28deg) translate(3px, -2px); }
    50% { transform: rotate(-28deg) translate(-2px, 3px); }
    75% { transform: rotate(-28deg) translate(2px, 1px); }
}

@keyframes starFloat13 {
    0%, 100% { transform: rotate(20deg) translate(0, 0); }
    25% { transform: rotate(20deg) translate(3px, -2px); }
    50% { transform: rotate(20deg) translate(-2px, 3px); }
    75% { transform: rotate(20deg) translate(2px, 1px); }
}

@keyframes starFloat14 {
    0%, 100% { transform: rotate(-40deg) translate(0, 0); }
    25% { transform: rotate(-40deg) translate(3px, -2px); }
    50% { transform: rotate(-40deg) translate(-2px, 3px); }
    75% { transform: rotate(-40deg) translate(2px, 1px); }
}

@keyframes starFloat15 {
    0%, 100% { transform: rotate(5deg) translate(0, 0); }
    25% { transform: rotate(5deg) translate(3px, -2px); }
    50% { transform: rotate(5deg) translate(-2px, 3px); }
    75% { transform: rotate(5deg) translate(2px, 1px); }
}

@keyframes starFloat16 {
    0%, 100% { transform: rotate(-15deg) translate(0, 0); }
    25% { transform: rotate(-15deg) translate(3px, -2px); }
    50% { transform: rotate(-15deg) translate(-2px, 3px); }
    75% { transform: rotate(-15deg) translate(2px, 1px); }
}







/* 不同颜色的SVG星星变体 */

/* 变体1: 金黄色星星 */
.star-variant-1::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_1" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFFF00">.</text></pattern><pattern id="p2_1" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFFF00">.</text></pattern><pattern id="p3_1" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFFF00">.</text></pattern><clipPath id="starClip1"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip1)"><rect width="24" height="24" fill="url(%23p1_1)"/><rect width="24" height="24" fill="url(%23p2_1)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_1)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23FFD700" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体2: 粉红色星星 */
.star-variant-2::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_2" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF1493">.</text></pattern><pattern id="p2_2" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF1493">.</text></pattern><pattern id="p3_2" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF1493">.</text></pattern><clipPath id="starClip2"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip2)"><rect width="24" height="24" fill="url(%23p1_2)"/><rect width="24" height="24" fill="url(%23p2_2)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_2)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23FF69B4" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体3: 天蓝色星星 */
.star-variant-3::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_3" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300BFFF">.</text></pattern><pattern id="p2_3" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300BFFF">.</text></pattern><pattern id="p3_3" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300BFFF">.</text></pattern><clipPath id="starClip3"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip3)"><rect width="24" height="24" fill="url(%23p1_3)"/><rect width="24" height="24" fill="url(%23p2_3)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_3)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%2387CEEB" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体4: 紫色星星 */
.star-variant-4::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_4" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF00FF">.</text></pattern><pattern id="p2_4" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF00FF">.</text></pattern><pattern id="p3_4" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF00FF">.</text></pattern><clipPath id="starClip4"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip4)"><rect width="24" height="24" fill="url(%23p1_4)"/><rect width="24" height="24" fill="url(%23p2_4)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_4)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23DA70D6" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体5: 橙色星星 */
.star-variant-5::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_5" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF4500">.</text></pattern><pattern id="p2_5" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF4500">.</text></pattern><pattern id="p3_5" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF4500">.</text></pattern><clipPath id="starClip5"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip5)"><rect width="24" height="24" fill="url(%23p1_5)"/><rect width="24" height="24" fill="url(%23p2_5)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_5)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23FF8C00" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体6: 青色星星 */
.star-variant-6::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_6" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300FFFF">.</text></pattern><pattern id="p2_6" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300FFFF">.</text></pattern><pattern id="p3_6" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300FFFF">.</text></pattern><clipPath id="starClip6"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip6)"><rect width="24" height="24" fill="url(%23p1_6)"/><rect width="24" height="24" fill="url(%23p2_6)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_6)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%2300CED1" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体7: 薰衣草色星星 */
.star-variant-7::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_7" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFFFFF">.</text></pattern><pattern id="p2_7" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFFFFF">.</text></pattern><pattern id="p3_7" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFFFFF">.</text></pattern><clipPath id="starClip7"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip7)"><rect width="24" height="24" fill="url(%23p1_7)"/><rect width="24" height="24" fill="url(%23p2_7)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_7)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23E6E6FA" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体8: 珊瑚色星星 */
.star-variant-8::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_8" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF6347">.</text></pattern><pattern id="p2_8" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF6347">.</text></pattern><pattern id="p3_8" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF6347">.</text></pattern><clipPath id="starClip8"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip8)"><rect width="24" height="24" fill="url(%23p1_8)"/><rect width="24" height="24" fill="url(%23p2_8)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_8)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23FF7F50" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 新增颜色变体 9-16 */

/* 变体9: 绿色星星 */
.star-variant-9::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="26" height="26" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_9" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300FF00">.</text></pattern><pattern id="p2_9" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300FF00">.</text></pattern><pattern id="p3_9" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300FF00">.</text></pattern><clipPath id="starClip9"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip9)"><rect width="24" height="24" fill="url(%23p1_9)"/><rect width="24" height="24" fill="url(%23p2_9)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_9)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%2332CD32" stroke-width="0.8" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体10: 红色星星 */
.star-variant-10::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_10" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF0000">.</text></pattern><pattern id="p2_10" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF0000">.</text></pattern><pattern id="p3_10" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF0000">.</text></pattern><clipPath id="starClip10"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip10)"><rect width="24" height="24" fill="url(%23p1_10)"/><rect width="24" height="24" fill="url(%23p2_10)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_10)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23DC143C" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体11: 蓝紫色星星 */
.star-variant-11::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_11" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%238A2BE2">.</text></pattern><pattern id="p2_11" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%238A2BE2">.</text></pattern><pattern id="p3_11" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%238A2BE2">.</text></pattern><clipPath id="starClip11"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip11)"><rect width="24" height="24" fill="url(%23p1_11)"/><rect width="24" height="24" fill="url(%23p2_11)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_11)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%239370DB" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体12: 深橙色星星 */
.star-variant-12::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_12" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF8C00">.</text></pattern><pattern id="p2_12" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF8C00">.</text></pattern><pattern id="p3_12" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF8C00">.</text></pattern><clipPath id="starClip12"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip12)"><rect width="24" height="24" fill="url(%23p1_12)"/><rect width="24" height="24" fill="url(%23p2_12)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_12)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23FF6347" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体13: 春绿色星星 */
.star-variant-13::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_13" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300FF7F">.</text></pattern><pattern id="p2_13" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300FF7F">.</text></pattern><pattern id="p3_13" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%2300FF7F">.</text></pattern><clipPath id="starClip13"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip13)"><rect width="24" height="24" fill="url(%23p1_13)"/><rect width="24" height="24" fill="url(%23p2_13)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_13)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%2300FA9A" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体14: 深粉色星星 */
.star-variant-14::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_14" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF1493">.</text></pattern><pattern id="p2_14" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF1493">.</text></pattern><pattern id="p3_14" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FF1493">.</text></pattern><clipPath id="starClip14"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip14)"><rect width="24" height="24" fill="url(%23p1_14)"/><rect width="24" height="24" fill="url(%23p2_14)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_14)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23FF69B4" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体15: 靛蓝色星星 */
.star-variant-15::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_15" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%234B0082">.</text></pattern><pattern id="p2_15" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%234B0082">.</text></pattern><pattern id="p3_15" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%234B0082">.</text></pattern><clipPath id="starClip15"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip15)"><rect width="24" height="24" fill="url(%23p1_15)"/><rect width="24" height="24" fill="url(%23p2_15)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_15)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%236A5ACD" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 变体16: 金橙色星星 */
.star-variant-16::before {
    background-image: url('data:image/svg+xml;utf8,<svg width="20" height="20" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="p1_16" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="scale(0.14)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFD700">.</text></pattern><pattern id="p2_16" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(30) scale(0.135)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFD700">.</text></pattern><pattern id="p3_16" patternUnits="userSpaceOnUse" width="1" height="1" patternTransform="rotate(60) scale(0.13)"><text x="0" y="0.9" font-size="1" font-family="monospace" fill="%23FFD700">.</text></pattern><clipPath id="starClip16"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z"/></clipPath></defs><g clip-path="url(%23starClip16)"><rect width="24" height="24" fill="url(%23p1_16)"/><rect width="24" height="24" fill="url(%23p2_16)" opacity="0.85"/><rect width="24" height="24" fill="url(%23p3_16)" opacity="0.70"/></g><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" fill="none" stroke="%23FFA500" stroke-width="0.5" stroke-linecap="round" stroke-linejoin="round"/></svg>');
}

/* 为不同变体添加独特的动画延迟 */
.star-variant-1 { animation-delay: 0s, 0.5s; }
.star-variant-2 { animation-delay: 1s, 2s; }
.star-variant-3 { animation-delay: 2s, 1s; }
.star-variant-4 { animation-delay: 3s, 3.5s; }
.star-variant-5 { animation-delay: 4s, 2.5s; }
.star-variant-6 { animation-delay: 5s, 4s; }
.star-variant-7 { animation-delay: 1.5s, 5.5s; }
.star-variant-8 { animation-delay: 2.5s, 1.5s; }
.star-variant-9 { animation-delay: 3.5s, 4.5s; }
.star-variant-10 { animation-delay: 0.8s, 3.2s; }
.star-variant-11 { animation-delay: 4.2s, 1.8s; }
.star-variant-12 { animation-delay: 1.2s, 5.2s; }
.star-variant-13 { animation-delay: 5.5s, 2.8s; }
.star-variant-14 { animation-delay: 2.8s, 4.8s; }
.star-variant-15 { animation-delay: 4.8s, 0.8s; }
.star-variant-16 { animation-delay: 0.2s, 3.8s; }

/* 为每个星星添加不同的闪烁节奏和带旋转的漂浮动画 */
.romantic-star:nth-child(1) {
    animation: starTwinkleDisappear 6s ease-in-out infinite, starFloat1 9s ease-in-out infinite;
}
.romantic-star:nth-child(2) {
    animation: starTwinkleDisappear 7s ease-in-out infinite, starFloat2 11s ease-in-out infinite;
}
.romantic-star:nth-child(3) {
    animation: starTwinkleDisappear 5s ease-in-out infinite, starFloat3 8s ease-in-out infinite;
}
.romantic-star:nth-child(4) {
    animation: starTwinkleDisappear 8s ease-in-out infinite, starFloat4 10s ease-in-out infinite;
}
.romantic-star:nth-child(5) {
    animation: starTwinkleDisappear 6.5s ease-in-out infinite, starFloat5 12s ease-in-out infinite;
}
.romantic-star:nth-child(6) {
    animation: starTwinkleDisappear 7.5s ease-in-out infinite, starFloat6 9.5s ease-in-out infinite;
}
.romantic-star:nth-child(7) {
    animation: starTwinkleDisappear 5.5s ease-in-out infinite, starFloat7 11.5s ease-in-out infinite;
}
.romantic-star:nth-child(8) {
    animation: starTwinkleDisappear 8.5s ease-in-out infinite, starFloat8 7.5s ease-in-out infinite;
}
.romantic-star:nth-child(9) {
    animation: starTwinkleDisappear 6.2s ease-in-out infinite, starFloat9 8.8s ease-in-out infinite;
}
.romantic-star:nth-child(10) {
    animation: starTwinkleDisappear 7.8s ease-in-out infinite, starFloat10 10.2s ease-in-out infinite;
}
.romantic-star:nth-child(11) {
    animation: starTwinkleDisappear 5.8s ease-in-out infinite, starFloat11 9.8s ease-in-out infinite;
}
.romantic-star:nth-child(12) {
    animation: starTwinkleDisappear 8.2s ease-in-out infinite, starFloat12 11.8s ease-in-out infinite;
}
.romantic-star:nth-child(13) {
    animation: starTwinkleDisappear 6.8s ease-in-out infinite, starFloat13 8.2s ease-in-out infinite;
}
.romantic-star:nth-child(14) {
    animation: starTwinkleDisappear 7.2s ease-in-out infinite, starFloat14 10.8s ease-in-out infinite;
}
.romantic-star:nth-child(15) {
    animation: starTwinkleDisappear 5.2s ease-in-out infinite, starFloat15 9.2s ease-in-out infinite;
}
.romantic-star:nth-child(16) {
    animation: starTwinkleDisappear 8.8s ease-in-out infinite, starFloat16 12.2s ease-in-out infinite;
}





/* 浪漫话语模态框 */
.romantic-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.romantic-modal-content {
    /* 完全透明背景 */
    background: transparent;
    border-radius: 25px;
    padding: 0;
    max-width: 600px;
    width: 90%;
    /* 发光边框效果 */
    box-shadow:
        0 0 30px rgba(236, 72, 153, 0.3),
        0 0 60px rgba(236, 72, 153, 0.2),
        0 0 90px rgba(236, 72, 153, 0.1);
    border: 2px solid rgba(236, 72, 153, 0.4);
    animation: modalAppear 0.5s ease-out;
}

@keyframes modalAppear {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.romantic-modal-header {
    padding: 25px 30px 15px;
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.romantic-modal-header h3 {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.8rem;
    margin: 0;
    /* 闪闪发光效果 */
    background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
    background-size: 400% 400% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: sparkle 2s ease-in-out infinite !important;
    text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
}

.close-romantic-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    font-family: 'Inter', 'ZiXiaoHunGouYu', sans-serif;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-romantic-btn:hover {
    background: rgba(255, 255, 255, 0.5);
    color: #333;
    transform: rotate(90deg);
}

.romantic-quote-content {
    padding: 40px;
    text-align: center;
    animation: fadeInContent 0.6s ease-out;
    position: relative;
    overflow: hidden;
    /* 透明背景 */
    background: transparent;
    /* 发光边框效果 */
    border: 2px solid rgba(236, 72, 153, 0.4);
    border-radius: 20px;
    box-shadow:
        0 0 20px rgba(236, 72, 153, 0.3),
        0 0 40px rgba(236, 72, 153, 0.2),
        0 0 60px rgba(236, 72, 153, 0.1),
        inset 0 0 20px rgba(236, 72, 153, 0.05);
}

/* 保留星星装饰 */
.romantic-quote-content::after {
    content: '✨';
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.2rem;
    opacity: 0.8;
    animation: sparkle 3s ease-in-out infinite;
    text-shadow: 0 0 10px rgba(236, 72, 153, 0.5);
}

@keyframes fadeInContent {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 梦幻浮动动画 */
@keyframes dreamyFloat {
    0%, 100% {
        transform: rotate(0deg) scale(1);
    }
    33% {
        transform: rotate(120deg) scale(1.1);
    }
    66% {
        transform: rotate(240deg) scale(0.9);
    }
}

/* 闪烁动画 */
@keyframes sparkle {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.2);
    }
}

.romantic-quote-content .quote-text {
    font-family: 'KaiTi', '楷体', 'STKaiti', 'Playfair Display', 'ZiXiaoHunGouYu', serif !important;
    font-size: 1.7rem !important;
    line-height: 2.2 !important;
    color: #ec4899 !important;
    margin: 0 0 30px 0 !important;
    text-shadow: 0 2px 6px rgba(236, 72, 153, 0.4) !important;
    font-style: normal !important;
    font-weight: 600 !important;
    letter-spacing: 1px !important;
    text-align: center !important;
    position: relative !important;
    padding: 25px 20px !important;
    /* 透明背景 */
    background: transparent !important;
    border-radius: 18px !important;
    /* 发光边框效果 */
    border: 1px solid rgba(236, 72, 153, 0.3) !important;
    /* 强制移除可能导致文字透明的属性 */
    -webkit-background-clip: initial !important;
    -webkit-text-fill-color: #ec4899 !important;
    background-clip: initial !important;
    box-shadow:
        0 0 15px rgba(236, 72, 153, 0.2),
        0 0 30px rgba(236, 72, 153, 0.1),
        inset 0 0 15px rgba(236, 72, 153, 0.05) !important;
}

/* 为诗句添加装饰性的引号 */
.romantic-quote-content .quote-text::before {
    content: '"';
    font-family: 'Times New Roman', serif;
    font-size: 3rem;
    color: rgba(236, 72, 153, 0.3);
    position: absolute;
    top: -5px;
    left: 10px;
    line-height: 1;
}

.romantic-quote-content .quote-text::after {
    content: '"';
    font-family: 'Times New Roman', serif;
    font-size: 3rem;
    color: rgba(236, 72, 153, 0.3);
    position: absolute;
    bottom: -15px;
    right: 10px;
    line-height: 1;
}

/* 作者和作品信息容器 */
.quote-attribution {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0 0 0;
    padding: 15px 20px;
    /* 透明背景 */
    background: transparent;
    border-radius: 15px;
    /* 发光边框效果 */
    border: 1px solid rgba(236, 72, 153, 0.25);
    box-shadow:
        0 0 10px rgba(236, 72, 153, 0.2),
        0 0 20px rgba(236, 72, 153, 0.1),
        inset 0 0 10px rgba(236, 72, 153, 0.03);
    position: relative;
    overflow: hidden;
}

/* 为作者和作品容器添加装饰 */
.quote-attribution::before {
    content: '💖';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.8rem;
    opacity: 0.1;
    z-index: 0;
}

.quote-attribution > * {
    position: relative;
    z-index: 1;
}

.romantic-quote-content .quote-author {
    font-family: 'Dancing Script', 'KaiTi', '楷体', 'STKaiti', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.3rem;
    color: #93c5fd; /* 很淡的蓝色作为回退颜色 */
    margin: 0;
    font-weight: 600;
    font-style: italic;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    text-shadow: 0 1px 3px rgba(147, 197, 253, 0.3);
    background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

/* 为不支持背景裁剪的浏览器提供回退 */
@supports not (-webkit-background-clip: text) {
    .romantic-quote-content .quote-author {
        color: #93c5fd;
        -webkit-text-fill-color: initial;
    }

    .romantic-quote-content .quote-work {
        color: #93c5fd;
        -webkit-text-fill-color: initial;
    }
}

.romantic-quote-content .quote-work {
    font-family: 'Playfair Display', 'SimSun', '宋体', 'ZiXiaoHunGouYu', serif;
    font-size: 1.1rem;
    color: #93c5fd; /* 很淡的蓝色作为回退颜色 */
    margin: 0;
    font-style: italic;
    font-weight: 500;
    opacity: 0.85;
    letter-spacing: 0.8px;
    transition: all 0.3s ease;
    text-shadow: 0 1px 3px rgba(147, 197, 253, 0.25);
    background: linear-gradient(135deg, #93c5fd 0%, #60a5fa 50%, #3b82f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

/* 为作者和作品信息添加悬停效果 */
.quote-attribution:hover {
    /* 保持透明背景 */
    background: transparent;
    transform: translateY(-3px);
    /* 增强发光效果 */
    box-shadow:
        0 0 15px rgba(236, 72, 153, 0.4),
        0 0 30px rgba(236, 72, 153, 0.2),
        0 0 45px rgba(236, 72, 153, 0.1),
        inset 0 0 15px rgba(236, 72, 153, 0.05);
    border: 1px solid rgba(236, 72, 153, 0.4);
}

.romantic-quote-content .quote-author:hover {
    transform: scale(1.08);
    text-shadow: 0 2px 6px rgba(147, 197, 253, 0.4);
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.romantic-quote-content .quote-work:hover {
    opacity: 1;
    transform: scale(1.08);
    text-shadow: 0 2px 6px rgba(147, 197, 253, 0.35);
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.romantic-modal-footer {
    padding: 15px 30px 25px;
    text-align: center;
}

.new-quote-btn {
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 30px;
    font-family: 'KaiTi', '楷体', 'Microsoft YaHei', 'ZiXiaoHunGouYu', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 15px rgba(96, 165, 250, 0.3),
        0 2px 8px rgba(59, 130, 246, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.new-quote-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.new-quote-btn:hover::before {
    left: 100%;
}

.new-quote-btn:hover {
    transform: translateY(-3px);
    box-shadow:
        0 8px 25px rgba(96, 165, 250, 0.4),
        0 4px 12px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%);
}

.new-quote-btn:active {
    transform: translateY(-1px);
    box-shadow:
        0 4px 15px rgba(96, 165, 250, 0.3),
        0 2px 8px rgba(59, 130, 246, 0.2);
}

/* 诗句切换动画 */
.quote-fade-in {
    animation: quoteFadeIn 0.8s ease-out;
}

@keyframes quoteFadeIn {
    0% {
        opacity: 0;
        transform: translateY(15px) scale(0.95);
    }
    50% {
        opacity: 0.7;
        transform: translateY(5px) scale(0.98);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* 为诗句添加微妙的悬停效果 */
.romantic-quote-content .quote-text:hover {
    transform: translateY(-2px);
    transition: all 0.3s ease;
    color: #be185d !important;
    /* 增强发光效果 */
    box-shadow:
        0 0 20px rgba(236, 72, 153, 0.4),
        0 0 40px rgba(236, 72, 153, 0.2),
        0 0 60px rgba(236, 72, 153, 0.1),
        inset 0 0 20px rgba(236, 72, 153, 0.08) !important;
    border: 1px solid rgba(236, 72, 153, 0.5) !important;
}

/* 增强流星效果 */
.shooting-stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.shooting-star {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #fff 0%, rgba(255,255,255,0.8) 30%, transparent 70%);
    border-radius: 50%;
    box-shadow: 0 0 10px #fff, 0 0 20px #fff, 0 0 30px #fff;
    animation-name: shoot;
    animation-timing-function: linear;
    animation-iteration-count: infinite;
}

.shooting-star::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, rgba(255,255,255,0.9), rgba(255,255,255,0.5), transparent);
    transform: translate(-100px, -50%);
    border-radius: 2px;
    box-shadow: 0 0 5px rgba(255,255,255,0.8);
}

/* 流星随机位置 - 改进的随机效果 */
.shooting-star:nth-child(1) {
    top: 8%;
    left: 12%;
    animation-delay: 0.5s;
    animation-duration: 4.2s;
}

.shooting-star:nth-child(2) {
    top: 23%;
    left: 78%;
    animation-delay: 1.8s;
    animation-duration: 3.8s;
}

.shooting-star:nth-child(3) {
    top: 41%;
    left: 31%;
    animation-delay: 3.2s;
    animation-duration: 4.9s;
}

.shooting-star:nth-child(4) {
    top: 17%;
    left: 89%;
    animation-delay: 0.9s;
    animation-duration: 3.4s;
}

.shooting-star:nth-child(5) {
    top: 62%;
    left: 19%;
    animation-delay: 2.5s;
    animation-duration: 4.6s;
}

.shooting-star:nth-child(6) {
    top: 34%;
    left: 67%;
    animation-delay: 4.1s;
    animation-duration: 3.9s;
}

.shooting-star:nth-child(7) {
    top: 79%;
    left: 43%;
    animation-delay: 1.3s;
    animation-duration: 4.1s;
}

.shooting-star:nth-child(8) {
    top: 56%;
    left: 82%;
    animation-delay: 3.7s;
    animation-duration: 5.2s;
}

.shooting-star:nth-child(9) {
    top: 91%;
    left: 26%;
    animation-delay: 0.2s;
    animation-duration: 3.7s;
}

.shooting-star:nth-child(10) {
    top: 14%;
    left: 58%;
    animation-delay: 2.9s;
    animation-duration: 4.4s;
}

@keyframes shoot {
    0% {
        transform: translateX(0) translateY(0) rotate(45deg) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateX(50px) translateY(50px) rotate(45deg) scale(1);
    }
    90% {
        opacity: 0.8;
        transform: translateX(350px) translateY(350px) rotate(45deg) scale(1.2);
    }
    100% {
        transform: translateX(400px) translateY(400px) rotate(45deg) scale(0.3);
        opacity: 0;
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* 浪漫漂浮元素动画 */
.hearts-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 3;
}

/* 爱心样式 */
.heart {
    position: absolute;
    color: rgba(255, 182, 193, 0.8);
    font-size: 20px;
    animation: float 8s infinite linear;
}

.heart::before {
    content: '💕';
}

/* 不同类型的浪漫元素 */
.heart.type-1::before { content: '💕'; color: rgba(255, 182, 193, 0.9); }
.heart.type-2::before { content: '💖'; color: rgba(255, 105, 180, 0.8); }
.heart.type-3::before { content: '💗'; color: rgba(255, 20, 147, 0.7); }
.heart.type-4::before { content: '💓'; color: rgba(255, 69, 0, 0.6); }
.heart.type-5::before { content: '💝'; color: rgba(255, 140, 0, 0.8); }
.heart.type-6::before { content: '💘'; color: rgba(255, 192, 203, 0.9); }
.heart.type-7::before { content: '💞'; color: rgba(255, 160, 122, 0.7); }
.heart.type-8::before { content: '💟'; color: rgba(255, 218, 185, 0.8); }
.heart.type-9::before { content: '❤️'; color: rgba(220, 20, 60, 0.8); }
.heart.type-10::before { content: '🌹'; color: rgba(255, 0, 0, 0.7); }
.heart.type-11::before { content: '🌸'; color: rgba(255, 182, 193, 0.8); }
.heart.type-12::before { content: '✨'; color: rgba(255, 215, 0, 0.9); }

/* 不同大小 */
.heart.small { font-size: 16px; }
.heart.medium { font-size: 24px; }
.heart.large { font-size: 32px; }

/* 位置分布 - 更多数量 */
.heart:nth-child(1) { left: 5%; animation-delay: 0s; animation-duration: 7s; }
.heart:nth-child(2) { left: 15%; animation-delay: 1s; animation-duration: 9s; }
.heart:nth-child(3) { left: 25%; animation-delay: 2s; animation-duration: 6s; }
.heart:nth-child(4) { left: 35%; animation-delay: 3s; animation-duration: 8s; }
.heart:nth-child(5) { left: 45%; animation-delay: 4s; animation-duration: 7s; }
.heart:nth-child(6) { left: 55%; animation-delay: 5s; animation-duration: 9s; }
.heart:nth-child(7) { left: 65%; animation-delay: 6s; animation-duration: 6s; }
.heart:nth-child(8) { left: 75%; animation-delay: 7s; animation-duration: 8s; }
.heart:nth-child(9) { left: 85%; animation-delay: 8s; animation-duration: 7s; }
.heart:nth-child(10) { left: 95%; animation-delay: 9s; animation-duration: 9s; }
.heart:nth-child(11) { left: 10%; animation-delay: 10s; animation-duration: 6s; }
.heart:nth-child(12) { left: 30%; animation-delay: 11s; animation-duration: 8s; }
.heart:nth-child(13) { left: 50%; animation-delay: 12s; animation-duration: 7s; }
.heart:nth-child(14) { left: 70%; animation-delay: 13s; animation-duration: 9s; }
.heart:nth-child(15) { left: 90%; animation-delay: 14s; animation-duration: 6s; }

@keyframes float {
    0% {
        transform: translateY(100vh) rotate(0deg) scale(0.5);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) rotate(10deg) scale(1);
    }
    50% {
        transform: translateY(50vh) rotate(180deg) scale(1.2);
        opacity: 0.8;
    }
    90% {
        opacity: 0.6;
        transform: translateY(10vh) rotate(350deg) scale(0.8);
    }
    100% {
        transform: translateY(-10vh) rotate(360deg) scale(0.3);
        opacity: 0;
    }
}

/* Header styles */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.title {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
    letter-spacing: 2px;
    color: white;
}

.title i {
    color: #ff6b9d;
    animation: title-heartbeat 1.5s infinite;
    margin: 0 15px;
    vertical-align: middle; /* 确保垂直对齐 */
}

@keyframes title-heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

@keyframes footer-heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

@keyframes smoothRainbowCycle {
    0% {
        background-position: 0% 50%;
    }
    25% {
        background-position: 100% 50%;
    }
    50% {
        background-position: 200% 50%;
    }
    75% {
        background-position: 300% 50%;
    }
    100% {
        background-position: 400% 50%;
    }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.subtitle {
    font-family: 'Courgette', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.3rem;
    font-weight: 400;
    opacity: 0.95;
    font-style: normal;
    color: #f8f9fa;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
    letter-spacing: 1px;
}

/* Love counter styles */
.love-counter {
    margin-bottom: 40px;
}

.counter-card {
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: none; /* 移除阴影 */
    border: none; /* 移除边框 */
    position: relative;
    overflow: hidden;
    /* 移除动画效果 */
}

.counter-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    animation: shimmer 3s linear infinite;
    pointer-events: none;
}

@keyframes cardGlow {
    0%, 100% {
        box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3),
                    0 0 30px rgba(255, 182, 193, 0.2);
    }
    50% {
        box-shadow: 0 25px 50px rgba(255, 105, 180, 0.5),
                    0 0 40px rgba(255, 182, 193, 0.4);
    }
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.counter-card h2 {
    font-family: 'ZiXiaoHunSanFen', 'Dancing Script', cursive;
    font-size: 2.6rem;
    color: #67e8f9 !important; /* 淡青色 */
    margin-bottom: 35px;
    font-weight: 600;
    letter-spacing: 0.8px;
    text-shadow: 0 3px 6px rgba(103, 232, 249, 0.3);
    transform: rotate(-1deg);
    transition: all 0.3s ease;
}

/* 添加倒计时标题的悬停效果 */
.counter-card h2:hover {
    transform: rotate(0deg) scale(1.02);
    color: #22d3ee !important; /* 悬停时稍微深一点的青色 */
    text-shadow: 0 4px 8px rgba(34, 211, 238, 0.4);
}

.counter-display {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.counter-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 100px;
}

.counter-number {
    font-family: 'ZiXiaoHunSanFen', 'Dancing Script', cursive;
    font-size: 4.5rem; /* 从3.8rem调大到4.5rem */
    font-weight: 700;
    color: #67e8f9 !important; /* 淡青色 */
    text-shadow: 0 2px 4px rgba(103, 232, 249, 0.3);
    letter-spacing: 2px;
}

.counter-label {
    font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif;
    font-size: 1.1rem;
    font-weight: 500;
    color: #67e8f9 !important; /* 淡青色 */
    margin-top: 8px;
    letter-spacing: 0.5px;
}

.start-date {
    font-family: 'ZiXiaoHunSanFen', 'Dancing Script', cursive;
    color: #67e8f9 !important; /* 淡青色 */
    font-weight: 600;
    font-size: 1.2rem;
    letter-spacing: 0.5px;
}

/* Birthday section styles */
.birthday-section {
    margin-bottom: 40px;
}

.birthday-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.birthday-card {
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    /* 移除磨砂效果，让背景完全透明 */
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.birthday-card:hover {
    transform: translateY(-8px) scale(1.02);
}

.birthday-card.girl {
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    box-shadow: 0 15px 35px rgba(255, 20, 147, 0.1),
                0 0 25px rgba(255, 105, 180, 0.08);
    animation: cardGlow 5s ease-in-out infinite;
}

.birthday-card.boy {
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    box-shadow: 0 15px 35px rgba(221, 160, 221, 0.1),
                0 0 25px rgba(230, 230, 250, 0.08);
    animation: cardGlow 5s ease-in-out infinite;
}

.birthday-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.15) 50%,
        transparent 70%);
    animation: shimmer 4s linear infinite;
    pointer-events: none;
}



.birthday-icon {
    font-size: 3rem;
    margin-bottom: 20px;
}

.birthday-card.girl .birthday-icon {
    color: #ff6b9d;
}

.birthday-card.boy .birthday-icon {
    color: #4ecdc4;
}

/* Yu的生日卡片样式 - 深蓝青色 */
.birthday-card.boy h3 {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.2rem;
    font-weight: 600;
    background: linear-gradient(45deg, #0ea5e9, #0284c7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #0ea5e9; /* fallback */
    margin-bottom: 15px;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
}

/* Wang的生日卡片样式 - 粉色 */
.birthday-card.girl h3 {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.2rem;
    font-weight: 600;
    background: linear-gradient(45deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #f093fb; /* fallback */
    margin-bottom: 15px;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(240, 147, 251, 0.2);
}

/* Special styling for names in birthday cards */
.name-yu {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #0ea5e9, #0284c7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #0ea5e9; /* fallback */
    text-shadow: 0 2px 4px rgba(14, 165, 233, 0.2);
}

.name-wang {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #f093fb; /* fallback */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Birthday text styling - matches the name style */
.name-yu + .birthday-text {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #0ea5e9, #0284c7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #0ea5e9; /* fallback */
    text-shadow: 0 2px 4px rgba(14, 165, 233, 0.2);
}

.name-wang + .birthday-text {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #f093fb; /* fallback */
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Yu的生日日期 - 深蓝青色 */
.birthday-card.boy .birthday-date {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 2.4rem;
    font-weight: 700;
    background: linear-gradient(45deg, #0ea5e9, #0284c7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #0ea5e9; /* fallback */
    margin-bottom: 15px;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
}

/* Wang的生日日期 - 粉色 */
.birthday-card.girl .birthday-date {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 2.4rem;
    font-weight: 700;
    background: linear-gradient(45deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #f093fb; /* fallback */
    margin-bottom: 15px;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(240, 147, 251, 0.2);
}

/* Yu的倒计时文字 - 深蓝青色 */
.birthday-card.boy .birthday-countdown {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    background: linear-gradient(45deg, #0ea5e9, #0284c7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #0ea5e9; /* fallback */
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(14, 165, 233, 0.3);
}

/* Wang的倒计时文字 - 粉色 */
.birthday-card.girl .birthday-countdown {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    background: linear-gradient(45deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #f093fb; /* fallback */
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(240, 147, 251, 0.2);
}

.countdown-days {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-weight: 700;
    background: linear-gradient(45deg,
        #ff6b9d, #c44569, #f8b500, #0ea5e9, #0284c7,
        #ff1744, #e91e63, #dc2626, #b91c1c, #3f51b5,
        #2196f3, #03a9f4, #00bcd4, #009688, #4caf50,
        #8bc34a, #cddc39, #ffeb3b, #ffc107, #ff9800,
        #ff5722, #ff6b9d);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #ff6b9d; /* fallback */
    font-size: 1.5rem;
    letter-spacing: 1px;
    text-shadow: 0 2px 4px rgba(255, 107, 157, 0.3);
    animation: smoothRainbowCycle 8s linear infinite;
}

/* Love quotes styles */
.love-quotes {
    margin-bottom: 40px;
}

.quote-card {
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(255, 127, 80, 0.1),
                0 0 30px rgba(255, 160, 122, 0.08);
    /* 移除磨砂效果，让背景完全透明 */
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    animation: cardGlow 4s ease-in-out infinite;
}

.quote-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    animation: shimmer 3s linear infinite;
    pointer-events: none;
}

.quote-icon {
    font-size: 2rem;
    color: #ff6b9d;
    margin-bottom: 20px;
}

/* 移除通用的 .quote-text 样式，避免与星星话语中的诗词样式冲突 */
.quote-text:not(.romantic-quote-content .quote-text) {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 2.2rem;
    background: linear-gradient(45deg, #67e8f9, #22d3ee);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #67e8f9; /* fallback */
    line-height: 1.5;
    margin-bottom: 30px;
    font-style: normal;
    font-weight: 600;
    letter-spacing: 0.8px;
    text-shadow: 0 3px 6px rgba(103, 232, 249, 0.3);
    transform: rotate(-1deg);
    transition: all 0.3s ease;
}

/* 添加情话文字的悬停效果 */
.quote-text:not(.romantic-quote-content .quote-text):hover {
    transform: rotate(0deg) scale(1.02);
    background: linear-gradient(45deg, #22d3ee, #0891b2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 4px 8px rgba(34, 211, 238, 0.4);
}

.quote-btn {
    background: transparent;
    color: #67e8f9;
    border: 2px solid #67e8f9;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-weight: 600;
    font-style: normal;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(103, 232, 249, 0.2);
    letter-spacing: 0.8px;
    transform: translateY(0); /* 默认状态不偏移 */
}

.quote-btn:hover {
    transform: translateY(-2px); /* 悬停时向上偏移 */
    background: rgba(103, 232, 249, 0.1);
    color: #22d3ee;
    border-color: #22d3ee;
    box-shadow: 0 6px 20px rgba(103, 232, 249, 0.3);
}

/* Memory section styles */
.memory-section {
    margin-bottom: 40px;
}

.memory-section h2 {
    text-align: center;
    color: white;
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 35px;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
    letter-spacing: 1px;
}

.memory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.memory-item {
    background: transparent; /* 透明背景 */
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 15px 35px rgba(103, 232, 249, 0.3),
                0 0 25px rgba(103, 232, 249, 0.2); /* 淡青色阴影 */
    /* 移除磨砂效果，让背景完全透明 */
    border: 2px solid rgba(103, 232, 249, 0.6); /* 淡青色边框 */
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: cardGlow 5s ease-in-out infinite;
    cursor: pointer;
}

.memory-item::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.15) 50%,
        transparent 70%);
    animation: shimmer 4s linear infinite;
    pointer-events: none;
}

.memory-item:hover {
    transform: translateY(-5px);
}

.memory-placeholder {
    color: #555;
}

.memory-placeholder i {
    font-size: 3.5rem;
    color: #67e8f9 !important; /* 淡青色图标 */
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(103, 232, 249, 0.3); /* 淡青色阴影 */
}

.memory-placeholder p {
    font-family: 'ZiXiaoHunSanFen', 'Dancing Script', cursive; /* 使用字小魂三分行楷字体 */
    font-size: 1.2rem;
    font-weight: 600;
    color: #67e8f9 !important; /* 淡青色文字 */
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(103, 232, 249, 0.3); /* 淡青色阴影 */
    margin: 0;
}

/* Footer styles */
.footer {
    text-align: center;
    color: white;
    padding: 20px 0;
    opacity: 0.8;
    font-family: 'Courgette', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.1rem;
    font-weight: 400;
}

.footer i {
    color: #ff6b9d;
    animation: footer-heartbeat 1.5s infinite;
    vertical-align: middle; /* 确保与文字对齐 */
}

/* Love Messages styles */
.love-messages-section {
    margin-bottom: 40px;
}

.love-messages-section h2 {
    text-align: center;
    color: white;
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 35px;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.4);
    letter-spacing: 1px;
}



/* Message input form */
.message-input-card {
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 20px 40px rgba(255, 218, 185, 0.1),
                0 0 30px rgba(255, 228, 196, 0.08);
    /* 移除磨砂效果，让背景完全透明 */
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    animation: cardGlow 4s ease-in-out infinite;
}

.message-input-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    animation: shimmer 3s linear infinite;
    pointer-events: none;
}

.form-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    margin-bottom: 25px;
}

.form-header i {
    font-size: 2rem;
    color: #ff6b9d;
    margin-bottom: 0;
}

.form-header h3 {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.7rem;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
    letter-spacing: 0.5px;
    margin: 0;
}

/* Author and template row */
.author-and-template-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 20px;
}

/* Author selection */
.author-selection {
    display: flex;
    justify-content: flex-end;
    gap: 30px;
    flex: 1;
}

.author-option {
    cursor: pointer;
}

.author-option input[type="radio"] {
    display: none;
}

.author-selection .author-label,
.author-option .author-label {
    display: flex !important;
    align-items: center !important;
    gap: 12px !important;
    padding: 16px 32px !important;
    border-radius: 35px !important;
    border: 3px solid transparent !important;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(45deg, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff) border-box !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease, background 0.2s ease !important;
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive !important;
    font-weight: 700 !important;
    font-size: 1.4rem !important;
    letter-spacing: 1.2px !important;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15) !important;
    cursor: pointer !important;
    position: relative !important;
    overflow: hidden !important;
    color: #ffffff !important;
    animation: rainbowBorder 3s linear infinite !important;
}

.author-selection .author-label.yu,
.author-option .author-label.yu {
    background: transparent !important; /* 透明背景 */
    border: 2px solid #67e8f9 !important; /* 淡青色边框 */
    color: #67e8f9 !important; /* 淡青色文字 */
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive !important; /* 还原原始字体 */
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    text-shadow: 0 2px 6px rgba(103, 232, 249, 0.4) !important; /* 淡青色阴影 */
    letter-spacing: 1.2px !important;
}

.author-selection .author-label.wang,
.author-option .author-label.wang {
    background: transparent !important; /* 透明背景 */
    border: 2px solid #67e8f9 !important; /* 淡青色边框 */
    color: #67e8f9 !important; /* 淡青色文字 */
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive !important; /* 还原原始字体 */
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    text-shadow: 0 2px 6px rgba(103, 232, 249, 0.4) !important; /* 淡青色阴影 */
    letter-spacing: 1.2px !important;
}

.author-selection .author-label.other,
.author-option .author-label.other {
    background: transparent !important; /* 透明背景 */
    border: 2px solid #67e8f9 !important; /* 淡青色边框 */
    color: #67e8f9 !important; /* 淡青色文字 */
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive !important; /* 还原原始字体 */
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    text-shadow: 0 2px 6px rgba(103, 232, 249, 0.4) !important; /* 淡青色阴影 */
    letter-spacing: 1.2px !important;
}

.author-option input[type="radio"]:checked + .author-label {
    transform: scale(1.12) !important;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3) !important;
    z-index: 15 !important;
    transition: transform 0.2s ease, box-shadow 0.2s ease !important;
    border: 4px solid transparent !important;
    animation: rainbowBorderSelected 2s linear infinite !important;
}

.author-option input[type="radio"]:checked + .author-label.yu {
    box-shadow: 0 15px 35px rgba(103, 232, 249, 0.6) !important; /* 淡青色阴影 */
    background: transparent !important; /* 保持透明背景 */
    border: 3px solid #67e8f9 !important; /* 加粗边框表示选中 */
}

.author-option input[type="radio"]:checked + .author-label.wang {
    box-shadow: 0 15px 35px rgba(103, 232, 249, 0.6) !important; /* 淡青色阴影 */
    background: transparent !important; /* 保持透明背景 */
    border: 3px solid #67e8f9 !important; /* 加粗边框表示选中 */
}

.author-option input[type="radio"]:checked + .author-label.other {
    box-shadow: 0 15px 35px rgba(103, 232, 249, 0.6) !important; /* 淡青色阴影 */
    background: transparent !important; /* 保持透明背景 */
    border: 3px solid #67e8f9 !important; /* 加粗边框表示选中 */
}

/* Enhanced hover effects for author labels */
.author-label {
    transition: transform 0.25s ease, box-shadow 0.25s ease !important;
    cursor: pointer !important;
    position: relative !important;
}



.author-label:hover {
    transform: translateY(-4px) scale(1.08) !important;
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.3) !important;
    z-index: 10 !important;
    animation: rainbowBorderFast 1.5s linear infinite !important;
}

.author-label.yu:hover {
    box-shadow: 0 12px 30px rgba(103, 232, 249, 0.5) !important; /* 淡青色阴影 */
    background: transparent !important; /* 保持透明背景 */
}

.author-label.wang:hover {
    box-shadow: 0 12px 30px rgba(103, 232, 249, 0.5) !important; /* 淡青色阴影 */
    background: transparent !important; /* 保持透明背景 */
}

.author-label.other:hover {
    box-shadow: 0 12px 30px rgba(103, 232, 249, 0.5) !important; /* 淡青色阴影 */
    background: transparent !important; /* 保持透明背景 */
}

/* Active (click) effects for author labels */
.author-label:active {
    transform: translateY(-2px) scale(1.05) !important;
    transition: all 0.1s ease !important;
}



.author-label.yu:active {
    box-shadow: 0 8px 20px rgba(103, 232, 249, 0.6) !important; /* 淡青色阴影 */
}

.author-label.wang:active {
    box-shadow: 0 8px 20px rgba(103, 232, 249, 0.6) !important; /* 淡青色阴影 */
}

.author-label.other:active {
    box-shadow: 0 8px 20px rgba(103, 232, 249, 0.6) !important; /* 淡青色阴影 */
}

/* 动态彩色边框动画 */
@keyframes rainbowBorder {
    0% {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(45deg, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff) border-box;
    }
    14% {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(45deg, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b9d) border-box;
    }
    28% {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(45deg, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4) border-box;
    }
    42% {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(45deg, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1) border-box;
    }
    57% {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(45deg, #feca57, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4) border-box;
    }
    71% {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(45deg, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57) border-box;
    }
    85% {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(45deg, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3) border-box;
    }
    100% {
        background: linear-gradient(white, white) padding-box,
                    linear-gradient(45deg, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff) border-box;
    }
}

/* 悬停时的快速彩虹边框 */
@keyframes rainbowBorderFast {
    0% {
        background: linear-gradient(135deg, #0ea5e9, #0284c7) padding-box,
                    linear-gradient(45deg, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff) border-box;
    }
    25% {
        background: linear-gradient(135deg, #0ea5e9, #0284c7) padding-box,
                    linear-gradient(45deg, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1) border-box;
    }
    50% {
        background: linear-gradient(135deg, #0ea5e9, #0284c7) padding-box,
                    linear-gradient(45deg, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57) border-box;
    }
    75% {
        background: linear-gradient(135deg, #0ea5e9, #0284c7) padding-box,
                    linear-gradient(45deg, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4) border-box;
    }
    100% {
        background: linear-gradient(135deg, #0ea5e9, #0284c7) padding-box,
                    linear-gradient(45deg, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff) border-box;
    }
}

/* 选中时的彩虹边框 */
@keyframes rainbowBorderSelected {
    0% {
        background: linear-gradient(135deg, #0ea5e9, #0284c7) padding-box,
                    linear-gradient(45deg, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff) border-box;
    }
    14% {
        background: linear-gradient(135deg, #0ea5e9, #0284c7) padding-box,
                    linear-gradient(45deg, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b9d) border-box;
    }
    28% {
        background: linear-gradient(135deg, #0ea5e9, #0284c7) padding-box,
                    linear-gradient(45deg, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4) border-box;
    }
    42% {
        background: linear-gradient(135deg, #0ea5e9, #0284c7) padding-box,
                    linear-gradient(45deg, #96ceb4, #feca57, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1) border-box;
    }
    57% {
        background: linear-gradient(135deg, #667eea, #764ba2) padding-box,
                    linear-gradient(45deg, #feca57, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4) border-box;
    }
    71% {
        background: linear-gradient(135deg, #667eea, #764ba2) padding-box,
                    linear-gradient(45deg, #ff9ff3, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57) border-box;
    }
    85% {
        background: linear-gradient(135deg, #667eea, #764ba2) padding-box,
                    linear-gradient(45deg, #54a0ff, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3) border-box;
    }
    100% {
        background: linear-gradient(135deg, #667eea, #764ba2) padding-box,
                    linear-gradient(45deg, #ff6b9d, #4ecdc4, #45b7d1, #96ceb4, #feca57, #ff9ff3, #54a0ff) border-box;
    }
}



/* Message templates */
.message-templates {
    text-align: left;
    flex-shrink: 0;
}

.template-btn {
    background: transparent;
    color: #86efac;
    border: 2px solid #86efac;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1rem;
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(134, 239, 172, 0.2);
    letter-spacing: 0.5px;
}

.template-btn:hover {
    transform: translateY(-2px);
    background: rgba(134, 239, 172, 0.1);
    color: #4ade80;
    border-color: #4ade80;
    box-shadow: 0 6px 20px rgba(134, 239, 172, 0.3);
}

/* Message textarea */
#messageText {
    width: 100%;
    min-height: 120px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.2); /* 半透明白色背景 */
    backdrop-filter: blur(10px); /* 添加磨砂效果 */
    border: 2px solid rgba(103, 232, 249, 0.4); /* 淡青色边框 */
    border-radius: 15px;
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(103, 232, 249, 0.2); /* 淡青色阴影 */
    position: relative;
    overflow: hidden;
}

#messageText::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    animation: shimmer 4s linear infinite;
    pointer-events: none;
}

#messageText:focus {
    outline: none;
    border-color: rgba(103, 232, 249, 0.8); /* 聚焦时淡青色边框 */
    box-shadow: 0 8px 25px rgba(103, 232, 249, 0.4),
                0 0 0 3px rgba(103, 232, 249, 0.2); /* 聚焦时淡青色阴影 */
    transform: translateY(-2px);
}

#messageText::placeholder {
    color: rgba(102, 102, 102, 0.8);
    font-style: italic;
}

/* Form actions */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.save-btn, .clear-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.save-btn {
    background: transparent;
    color: #86efac;
    border: 2px solid #86efac;
    box-shadow: 0 4px 15px rgba(134, 239, 172, 0.2);
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.save-btn:hover {
    transform: translateY(-2px);
    background: rgba(134, 239, 172, 0.1);
    color: #4ade80;
    border-color: #4ade80;
    box-shadow: 0 6px 20px rgba(134, 239, 172, 0.3);
}

.clear-btn {
    background: transparent;
    color: #86efac;
    border: 2px solid #86efac;
    box-shadow: 0 4px 15px rgba(134, 239, 172, 0.2);
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-weight: 600;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.clear-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.clear-btn:hover {
    background: rgba(134, 239, 172, 0.1);
    color: #4ade80;
    border-color: #4ade80;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(134, 239, 172, 0.3);
}

.clear-btn:hover::before {
    left: 100%;
}

/* Templates modal */
.templates-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
    backdrop-filter: blur(10px);
}

.templates-modal.show {
    opacity: 1;
}

.modal-content {
    /* 完全透明背景 */
    background: transparent;
    border-radius: 25px;
    padding: 0;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
    margin: 20px;
    width: 90%;
    /* 发光边框效果 */
    box-shadow:
        0 0 30px rgba(236, 72, 153, 0.3),
        0 0 60px rgba(236, 72, 153, 0.2),
        0 0 90px rgba(236, 72, 153, 0.1);
    border: 2px solid rgba(236, 72, 153, 0.4);
    transform: scale(0.9);
    transition: transform 0.3s ease;
    animation: modalAppear 0.5s ease-out;
}

.templates-modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding: 25px 30px 15px;
    border-bottom: 1px solid rgba(148, 163, 184, 0.2);
}

.modal-header h3 {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.8rem;
    margin: 0;
    /* 闪闪发光效果 */
    background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
    background-size: 400% 400% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: sparkle 2s ease-in-out infinite !important;
    text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #999;
    cursor: pointer;
    font-family: 'Inter', 'ZiXiaoHunGouYu', sans-serif;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #f0f0f0;
    color: #333;
}

/* Template categories */
.template-category {
    margin-bottom: 30px;
}

.template-category h4 {
    color: #ff6b9d;
    font-size: 1.2rem;
    margin-bottom: 15px;
    text-align: center;
}

.template-messages {
    display: grid;
    gap: 10px;
}

.template-item {
    padding: 15px;
    border: 2px solid #f0f0f0;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.template-item:hover {
    border-color: #ff6b9d;
    background: rgba(255, 107, 157, 0.05);
    transform: translateY(-2px);
}

.template-item i {
    color: #ff6b9d;
    margin-top: 2px;
}

.template-item p {
    margin: 0;
    line-height: 1.5;
    font-size: 1.2rem !important; /* 增大字体 */
    /* 闪闪发光效果 */
    background: linear-gradient(45deg, #ff6b9d, #67e8f9, #ff6b9d, #67e8f9) !important;
    background-size: 400% 400% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: sparkle 2s ease-in-out infinite !important;
    text-shadow: 0 0 10px rgba(255, 107, 157, 0.5), 0 0 20px rgba(103, 232, 249, 0.3) !important;
}

/* Messages container */
.messages-container {
    background: transparent; /* 完全透明，让花朵背景完全显示 */
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(221, 160, 221, 0.1),
                0 0 30px rgba(230, 230, 250, 0.08);
    /* 移除磨砂效果，让背景完全透明 */
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    animation: cardGlow 4s ease-in-out infinite;
}

.messages-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 70%);
    animation: shimmer 3s linear infinite;
    pointer-events: none;
}

.messages-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.messages-header h3 {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.7rem;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.8);
    letter-spacing: 0.5px;
}

.messages-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 8px 16px;
    background: transparent; /* 透明背景 */
    border: 2px solid rgba(103, 232, 249, 0.4); /* 淡青色边框 */
    color: #67e8f9; /* 淡青色文字 */
    border-radius: 20px;
    font-size: 0.9rem;
    font-family: 'Inter', 'ZiXiaoHunGouYu', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(103, 232, 249, 0.1); /* 淡青色阴影 */
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.25),
        transparent);
    transition: left 0.5s ease;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    background: transparent; /* 保持透明背景 */
    border-color: #67e8f9; /* 悬停时加强淡青色边框 */
    color: #67e8f9; /* 保持淡青色文字 */
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(103, 232, 249, 0.2); /* 增强淡青色阴影 */
}

/* Collapse button */
.collapse-btn {
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    background: white;
    color: #666;
    border-radius: 50%;
    font-size: 1rem;
    font-family: 'Inter', 'ZiXiaoHunGouYu', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.collapse-btn:hover {
    border-color: #ff6b9d;
    color: #ff6b9d;
    transform: translateY(-2px);
    background: linear-gradient(135deg, #fff 0%, #ffe8f0 100%);
}

.collapse-btn i {
    transition: transform 0.3s ease;
}

.collapse-btn.collapsed i {
    transform: rotate(180deg);
}

/* Collapsible content */
.messages-collapsible-content {
    max-height: 1200px; /* 增加高度以显示9个栏目 */
    transition: max-height 0.4s ease, opacity 0.3s ease, padding 0.3s ease, margin 0.3s ease;
    overflow-y: auto; /* 启用垂直滚动 */
    overflow-x: hidden; /* 隐藏水平滚动 */
    opacity: 1;
    /* 自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 107, 157, 0.5) rgba(255, 255, 255, 0.1);
}

/* Webkit浏览器滚动条样式 */
.messages-collapsible-content::-webkit-scrollbar {
    width: 8px;
}

.messages-collapsible-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.messages-collapsible-content::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.6), rgba(255, 107, 157, 0.8));
    border-radius: 4px;
    transition: background 0.3s ease;
}

.messages-collapsible-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.8), rgba(255, 107, 157, 1));
}

.messages-collapsible-content.collapsed {
    max-height: 0;
    opacity: 0;
    padding-top: 0;
    padding-bottom: 0;
    margin-top: 0;
    margin-bottom: 0;
}

/* Messages list */
.messages-list {
    display: flex;
    flex-direction: column;
    gap: 12px !important;
}

/* Message group styles */
.message-group {
    margin-bottom: 8px;
}

.group-header {
    background: transparent;
    border: 2px solid transparent;
    background-clip: padding-box;
    border-radius: 20px;
    padding: 16px 20px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-bottom: 12px;
    /* 移除磨砂效果，让背景完全透明 */
    box-shadow:
        0 8px 32px rgba(255, 182, 193, 0.1),
        0 4px 16px rgba(221, 160, 221, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.group-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 182, 193, 0.05) 0%,
        rgba(255, 192, 203, 0.1) 25%,
        rgba(221, 160, 221, 0.05) 50%,
        rgba(230, 230, 250, 0.1) 75%,
        rgba(255, 240, 245, 0.05) 100%);
    border-radius: 18px;
    z-index: -1;
}

.group-header::after {
    content: '💕';
    position: absolute;
    top: 50%;
    right: 140px;
    transform: translateY(-50%);
    font-size: 1.2rem;
    opacity: 0.3;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.group-header:hover {
    background: linear-gradient(135deg,
        rgba(255, 182, 193, 0.25) 0%,
        rgba(255, 192, 203, 0.3) 25%,
        rgba(221, 160, 221, 0.25) 50%,
        rgba(230, 230, 250, 0.3) 75%,
        rgba(255, 240, 245, 0.25) 100%);
    transform: translateY(-2px);
    box-shadow:
        0 12px 40px rgba(255, 182, 193, 0.15),
        0 6px 20px rgba(221, 160, 221, 0.12),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.group-header:hover::after {
    opacity: 0.6;
    transform: translateY(-50%) scale(1.1);
}

.group-header.collapsed {
    margin-bottom: 0;
}

.group-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.group-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 600;
    color: #4a5568;
    font-size: 1rem;
    position: relative;
    z-index: 1;
    flex: 1;
    cursor: pointer;
}

.group-title i {
    color: #e91e63;
    transition: all 0.3s ease;
    font-size: 1rem;
    text-shadow: 0 1px 2px rgba(233, 30, 99, 0.2);
}

.title-content {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.main-title {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b9d, #c44569, #f8b500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #ff6b9d;
    text-shadow: 0 2px 4px rgba(255, 107, 157, 0.2);
    letter-spacing: 0.5px;
}

.title-heart {
    color: #e91e63;
    font-size: 1.1rem;
    animation: heartbeat 2s ease-in-out infinite;
    filter: drop-shadow(0 2px 4px rgba(233, 30, 99, 0.3));
    position: absolute;
    right: 70px; /* 相对于删除按钮左侧75px位置 */
    top: 50%; /* 垂直居中于group-header */
    transform: translateY(-50%); /* 精确垂直居中 */
    z-index: 2;
    line-height: 1; /* 确保行高一致 */
}

@keyframes heartbeat {
    0%, 100% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.15); }
}

.sub-title {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.1rem;
    font-weight: 600;
    background: linear-gradient(45deg, #ff9a9e, #fecfef, #fecfef);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #ff9a9e;
    letter-spacing: 0.8px;
    text-shadow: 0 1px 3px rgba(255, 154, 158, 0.3);
}

.group-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    position: relative;
    z-index: 10;
}

.delete-group-btn {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1), rgba(220, 38, 38, 0.15));
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
    padding: 6px 8px;
    color: #dc2626;
    font-family: 'Inter', 'ZiXiaoHunGouYu', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    /* 移除磨砂效果，让背景完全透明 */
    position: relative;
    z-index: 15;
    pointer-events: auto;
}

.delete-group-btn:hover {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.25));
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
}

.message-count {
    color: #67e8f9; /* 淡青色文字 */
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-weight: 600;
    font-size: 1.1rem;
    background: transparent; /* 透明背景 */
    padding: 4px 8px;
    border-radius: 12px;
    border: none; /* 无边框 */
}

.group-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    max-height: none;
}

.group-content.collapsed {
    max-height: 0;
    opacity: 0;
    margin-top: 0;
    gap: 0;
}

.group-content .message-card {
    margin-left: 20px;
    position: relative;
}

.group-content .message-card::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e2e8f0, transparent);
}

/* Month group styles (三级折叠) */
.month-group {
    margin-bottom: 6px;
    margin-left: 20px;
    position: relative;
}

.month-group::before {
    content: '';
    position: absolute;
    left: -15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, rgba(255, 182, 193, 0.3), transparent);
}

.month-header {
    background: transparent;
    border: 1px solid transparent;
    border-radius: 15px;
    padding: 12px 16px;
    margin-bottom: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.month-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 192, 203, 0.03) 0%,
        rgba(221, 160, 221, 0.05) 50%,
        rgba(255, 240, 245, 0.03) 100%);
    z-index: -1;
}

.month-header:hover {
    background: linear-gradient(135deg,
        rgba(255, 192, 203, 0.18) 0%,
        rgba(221, 160, 221, 0.2) 25%,
        rgba(230, 230, 250, 0.18) 50%,
        rgba(255, 240, 245, 0.2) 75%,
        rgba(255, 182, 193, 0.18) 100%);
    transform: translateY(-1px);
    box-shadow:
        0 8px 25px rgba(255, 182, 193, 0.1),
        0 4px 15px rgba(221, 160, 221, 0.08);
}

.month-header.collapsed {
    margin-bottom: 0;
}

.month-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.month-title {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
    font-size: 0.95rem;
    color: #6b7280;
    cursor: pointer;
    flex: 1;
    min-width: 0;
}

.month-title i {
    color: #ec4899;
    transition: transform 0.3s ease;
    font-size: 0.9rem;
}

.month-title-content {
    display: flex;
    flex-direction: column;
    gap: 2px;
    flex: 1;
    min-width: 0;
}

.month-main-title {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b9d, #c44569, #f8b500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #ff6b9d;
    text-shadow: 0 2px 4px rgba(255, 107, 157, 0.2);
    letter-spacing: 0.5px;
}

.month-heart {
    font-size: 1rem;
    margin-left: auto;
    margin-right: 8px;
}

.month-message-count {
    font-size: 0.85rem;
    color: #9ca3af;
    background: rgba(255, 182, 193, 0.15);
    padding: 2px 8px;
    border-radius: 12px;
    font-weight: 500;
}

.month-actions {
    display: flex;
    gap: 4px;
    align-items: center;
}

.delete-month-btn {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.2);
    color: #dc2626;
    border-radius: 8px;
    padding: 4px 6px;
    font-family: 'Inter', 'ZiXiaoHunGouYu', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    opacity: 0.7;
}

.delete-month-btn:hover {
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
    opacity: 1;
    transform: scale(1.05);
}

.month-content {
    display: flex;
    flex-direction: column;
    gap: 6px;
    overflow: hidden;
    transition: all 0.3s ease;
    max-height: none;
}

.month-content.collapsed {
    max-height: 0;
    opacity: 0;
    margin-top: 0;
    gap: 0;
}

.month-content .message-card {
    margin-left: 15px;
    position: relative;
}

.month-content .message-card::before {
    content: '';
    position: absolute;
    left: -12px;
    top: 0;
    bottom: 0;
    width: 1px;
    background: linear-gradient(to bottom, rgba(255, 182, 193, 0.2), transparent);
}

.message-card {
    border-radius: 15px;
    padding: 6px 12px !important; /* 减少内边距使其更紧凑 */
    position: relative;
    transition: all 0.3s ease;
    border-left: 4px solid;
    font-family: 'Inter', sans-serif;
    box-shadow: none; /* 移除阴影 */
    background: transparent; /* 确保背景透明 */
}

.message-card:hover {
    transform: translateY(-2px); /* 减少悬停效果 */
    box-shadow: none; /* 移除悬停阴影 */
}

.yu-message {
    background: transparent;
    border-left-color: #3b82f6;
    color: #1e40af;
}

.wang-message {
    background: transparent;
    border-left-color: #ec4899;
    color: #be185d;
}

.other-message {
    background: transparent;
    border-left-color: #f59e0b;
    color: #92400e;
}

.message-header {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    margin-bottom: 2px !important; /* 减少底部间距 */
}

.message-author {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: #2c3e50;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
    justify-self: start; /* 左对齐 */
}

.message-author i {
    color: #ff6b9d;
}

/* Author name styling */
.author-name {
    font-family: 'Dancing Script', cursive;
    font-size: 1.4rem;
    font-weight: 700;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.author-name.author-yu {
    background: linear-gradient(45deg, #0ea5e9, #0284c7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #0ea5e9; /* fallback for browsers that don't support background-clip */
}

.author-name.author-wang {
    background: linear-gradient(45deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #f093fb; /* fallback for browsers that don't support background-clip */
}

.author-name.author-other {
    background: linear-gradient(45deg, #fdcb6e, #e17055);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #fdcb6e; /* fallback for browsers that don't support background-clip */
}

.message-date {
    color: #67e8f9 !important; /* 统一使用淡青色 */
    font-size: 0.85rem; /* 稍微减小字体 */
    text-align: center; /* 居中显示时间 */
    justify-self: center; /* 在grid中居中 */
    font-family: 'Inter', sans-serif; /* 时间使用原来的字体 */
}

.message-actions {
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.3s ease;
    justify-self: end; /* 右对齐 */
}

.message-card:hover .message-actions {
    opacity: 1;
}

.edit-btn, .delete-btn {
    background: none;
    border: none;
    padding: 5px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #666;
    font-family: 'Inter', 'ZiXiaoHunGouYu', sans-serif;
}

.edit-btn:hover {
    background: rgba(255, 255, 255, 0.8);
    color: #4ecdc4;
}

.delete-btn:hover {
    background: rgba(255, 255, 255, 0.8);
    color: #ff6b9d;
}

.message-content {
    color: #d4af37 !important; /* 淡金色 */
    line-height: 1.3 !important; /* 减少行高 */
    font-size: 1rem !important; /* 稍微减小字体 */
    white-space: pre-wrap !important; /* 保持换行和空格 */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* 调整阴影以适配金色文字 */
    margin-top: 0 !important; /* 移除顶部间距 */
    /* 实现首行缩进但自动换行顶格的效果 */
    padding-left: 0 !important; /* 不给整体左边距 */
    text-indent: 2em !important; /* 只让第一行缩进 */
    font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif !important; /* 留言内容使用三分行楷字体 */
    /* 添加淡粉色背景 */
    background: linear-gradient(135deg,
        rgba(255, 182, 193, 0.12) 0%,
        rgba(255, 192, 203, 0.18) 25%,
        rgba(221, 160, 221, 0.12) 50%,
        rgba(230, 230, 250, 0.18) 75%,
        rgba(255, 240, 245, 0.12) 100%) !important;
    padding: 8px 12px !important; /* 给内容添加内边距 */
    border-radius: 8px !important; /* 圆角 */
    margin-top: 6px !important; /* 与头部保持间距 */
}

/* 多行消息中的每一行样式 */
.message-line {
    text-indent: 2em !important; /* 每行都缩进 */
    margin: 0 !important;
    padding: 0 !important;
}

/* Empty state */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #999;
}

.empty-state i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state p {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.3rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Success message */
.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #ff6b9d, #ff8a80);
    color: white;
    padding: 15px 25px;
    border-radius: 25px;
    box-shadow: 0 10px 30px rgba(255, 107, 157, 0.3);
    z-index: 1001;
    display: flex;
    align-items: center;
    gap: 10px;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.success-message.show {
    transform: translateX(0);
}

.success-message i {
    animation: heartbeat 1s infinite;
}

/* Romantic Confirmation Modal */
.romantic-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 20000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.romantic-confirm-modal.show {
    opacity: 1;
}

.romantic-confirm-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.romantic-confirm-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg,
        rgba(255, 240, 245, 0.98) 0%,
        rgba(255, 228, 225, 0.98) 30%,
        rgba(255, 218, 185, 0.98) 70%,
        rgba(255, 192, 203, 0.98) 100%);
    border-radius: 30px;
    padding: 35px;
    max-width: 480px;
    width: 90%;
    box-shadow:
        0 25px 80px rgba(255, 107, 157, 0.4),
        0 15px 40px rgba(255, 182, 193, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.6),
        0 0 0 1px rgba(255, 107, 157, 0.1);
    backdrop-filter: blur(25px);
    border: 3px solid transparent;
    background-clip: padding-box;
    animation: romanticModalAppear 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    position: relative;
    overflow: hidden;
}

.romantic-confirm-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg,
        rgba(255, 107, 157, 0.05) 0%,
        rgba(255, 182, 193, 0.05) 25%,
        rgba(255, 218, 185, 0.05) 50%,
        rgba(255, 192, 203, 0.05) 75%,
        rgba(255, 107, 157, 0.05) 100%);
    border-radius: inherit;
    z-index: -1;
    animation: shimmerBackground 4s ease-in-out infinite;
}

@keyframes shimmerBackground {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.02);
    }
}

.romantic-confirm-header {
    text-align: center;
    margin-bottom: 20px;
    position: relative;
}

.floating-hearts {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
}

.floating-hearts span {
    font-size: 1.5rem;
    animation: floatHeart 3s ease-in-out infinite;
}

.floating-hearts span:nth-child(1) { animation-delay: 0s; }
.floating-hearts span:nth-child(2) { animation-delay: 0.5s; }
.floating-hearts span:nth-child(3) { animation-delay: 1s; }
.floating-hearts span:nth-child(4) { animation-delay: 1.5s; }

@keyframes floatHeart {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-10px) scale(1.1); }
}

.romantic-confirm-header h3 {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #ff6b9d, #c44569, #f8b500);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: #ff6b9d;
    margin: 25px 0 0 0;
    text-shadow: 0 2px 4px rgba(255, 107, 157, 0.2);
}

.romantic-confirm-body {
    margin: 20px 0;
    text-align: center;
}

.romantic-confirm-body p {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.2rem;
    font-weight: 600;
    color: #4a5568;
    line-height: 1.6;
    margin: 0;
}

.romantic-confirm-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.romantic-btn {
    font-family: 'Dancing Script', 'ZiXiaoHunGouYu', cursive;
    font-size: 1.2rem;
    font-weight: 700;
    padding: 14px 28px;
    border: none;
    border-radius: 30px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 130px;
    justify-content: center;
    position: relative;
    overflow: hidden;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.romantic-btn-cancel {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.15), rgba(107, 114, 128, 0.25));
    color: #6b7280;
    border: 2px solid rgba(156, 163, 175, 0.3);
    box-shadow: 0 4px 15px rgba(156, 163, 175, 0.15);
}

.romantic-btn-cancel::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.romantic-btn-cancel:hover {
    background: linear-gradient(135deg, rgba(156, 163, 175, 0.25), rgba(107, 114, 128, 0.35));
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 25px rgba(156, 163, 175, 0.25);
    color: #4b5563;
}

.romantic-btn-cancel:hover::before {
    left: 100%;
}

.romantic-btn-confirm {
    background: linear-gradient(135deg, #ff6b9d, #ff8a80, #ffb74d);
    color: white;
    border: 2px solid rgba(255, 107, 157, 0.4);
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
}

.romantic-btn-confirm::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.romantic-btn-confirm:hover {
    background: linear-gradient(135deg, #ff5a8a, #ff7a70, #ffa726);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 30px rgba(255, 107, 157, 0.5);
}

.romantic-btn-confirm:hover::before {
    left: 100%;
}

/* Confetti Effect */
.confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 15000;
    overflow: hidden;
}

.confetti-piece {
    position: absolute;
    border-radius: 3px;
    pointer-events: none;
}

@keyframes confettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* Responsive design for messages */
@media (max-width: 768px) {
    .love-messages-section h2 {
        font-size: 2.2rem;
    }

    .memory-section h2 {
        font-size: 2.2rem;
    }

    .counter-card h2 {
        font-size: 2rem;
    }

    .form-header h3 {
        font-size: 1.5rem;
    }

    .messages-header h3 {
        font-size: 1.5rem;
    }

    .birthday-card h3 {
        font-size: 1.5rem;
    }

    .birthday-countdown {
        font-size: 1.1rem;
    }

    .empty-state p {
        font-size: 1.2rem;
    }

    /* Romantic confirm modal mobile styles */
    .romantic-confirm-content {
        padding: 25px 20px;
        max-width: 350px;
    }

    .romantic-confirm-header h3 {
        font-size: 1.7rem;
    }

    .romantic-confirm-body p {
        font-size: 1.1rem;
    }

    .romantic-btn {
        font-size: 1rem;
        padding: 10px 20px;
        min-width: 100px;
    }

    .floating-hearts span {
        font-size: 1.2rem;
    }

    .message-input-card,
    .messages-container {
        padding: 20px;
        margin: 20px 10px;
    }

    .author-and-template-row {
        flex-direction: column;
        gap: 20px;
        align-items: center;
    }

    .author-selection {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .message-templates {
        text-align: center;
    }

    .form-actions {
        flex-direction: column;
    }

    .messages-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .messages-actions {
        justify-content: center;
    }

    .modal-content {
        margin: 10px;
        padding: 20px;
        max-height: 90vh;
    }

    .template-messages {
        grid-template-columns: 1fr;
    }

    /* 移动端消息头部布局 - 使用grid布局保持时间居中 */
    .message-header {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        align-items: center;
        gap: 8px;
        margin-bottom: 2px !important;
    }

    .message-author {
        flex: 1;
    }

    .message-date {
        font-size: 0.8rem;
        color: #999;
        margin-right: 8px;
    }

    .message-actions {
        gap: 6px;
    }

    .group-header {
        padding: 12px 16px;
        border-radius: 16px;
    }

    .group-header::after {
        right: 110px;
        font-size: 1rem;
    }

    .group-title {
        font-size: 0.95rem;
        gap: 10px;
    }

    .main-title {
        font-size: 1.1rem;
    }

    .title-heart {
        font-size: 1rem;
    }

    .sub-title {
        font-size: 1rem;
    }

    .delete-group-btn {
        padding: 5px 6px;
        font-size: 0.8rem;
    }

    .message-count {
        font-size: 1rem;
        padding: 3px 6px;
    }

    .group-content .message-card {
        margin-left: 12px;
    }

    .group-content .message-card::before {
        left: -8px;
    }

    .message-actions {
        opacity: 1;
    }

    .message-card {
        padding: 6px 12px !important;
    }

    /* 移动端月份分组样式 */
    .month-group {
        margin-left: 12px;
    }

    .month-group::before {
        left: -8px;
        width: 1px;
    }

    .month-header {
        padding: 10px 14px;
        border-radius: 12px;
    }

    .month-title {
        font-size: 0.9rem;
        gap: 8px;
    }

    .month-main-title {
        font-size: 1.1rem;
    }

    .month-heart {
        font-size: 0.9rem;
    }

    .month-message-count {
        font-size: 0.8rem;
        padding: 2px 6px;
    }

    .delete-month-btn {
        padding: 3px 5px;
        font-size: 0.75rem;
    }

    .month-content .message-card {
        margin-left: 10px;
    }

    .month-content .message-card::before {
        left: -6px;
    }

    /* 移动端消息内容布局 */
    .message-content {
        grid-row: 2;
        grid-column: 1 / -1;
        margin-top: 8px;
        padding-left: 0 !important;
        text-indent: 2em !important;
    }

    /* 移动端折叠按钮优化 */
    .collapse-btn {
        width: 36px;
        height: 36px;
        padding: 6px;
        font-size: 0.9rem;
        background: linear-gradient(135deg,
            rgba(255, 182, 193, 0.1) 0%,
            rgba(255, 192, 203, 0.15) 50%,
            rgba(221, 160, 221, 0.1) 100%);
        border: 2px solid rgba(255, 182, 193, 0.3);
        /* 移除磨砂效果，让背景完全透明 */
    }

    .collapse-btn:hover {
        background: linear-gradient(135deg,
            rgba(255, 182, 193, 0.2) 0%,
            rgba(255, 192, 203, 0.25) 50%,
            rgba(221, 160, 221, 0.2) 100%);
        border-color: #ff6b9d;
        transform: translateY(-1px);
    }

    /* 移动端导出和清空按钮透明背景 */
    .action-btn {
        background: transparent !important; /* 透明背景 */
        border: 2px solid rgba(103, 232, 249, 0.4) !important; /* 淡青色边框 */
        color: #67e8f9 !important; /* 淡青色文字 */
        position: relative;
        overflow: hidden;
    }

    .action-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent);
        transition: left 0.5s ease;
    }

    .action-btn:hover::before {
        left: 100%;
    }

    .action-btn:hover {
        background: transparent !important; /* 保持透明背景 */
        border-color: #67e8f9 !important; /* 淡青色边框 */
        color: #67e8f9 !important; /* 淡青色文字 */
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(103, 232, 249, 0.2); /* 淡青色阴影 */
    }

    /* 移动端分组头部优化 */
    .group-header {
        padding: 10px 14px;
        border-radius: 14px;
        cursor: pointer;
        touch-action: manipulation;
    }

    .group-title {
        cursor: pointer;
        flex: 1;
        min-width: 0;
    }

    .group-actions {
        flex-shrink: 0;
        margin-left: 8px;
    }

    .delete-group-btn {
        padding: 4px 5px;
        font-size: 0.75rem;
        min-width: 28px;
        height: 28px;
    }

    /* 确保移动端点击区域足够大 */
    .group-title,
    .collapse-btn,
    .action-btn,
    .delete-group-btn,
    .edit-btn,
    .delete-btn {
        min-height: 44px;
        min-width: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        touch-action: manipulation;
    }

    /* 移动端消息操作按钮优化 */
    .edit-btn,
    .delete-btn {
        padding: 6px 8px;
        font-size: 0.8rem;
        min-width: 36px;
        height: 32px;
        border-radius: 8px;
    }

    /* 移动端特殊优化 - 确保触摸友好 */
    @media (max-width: 480px) {
        .message-header {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            gap: 4px;
            margin-bottom: 4px !important;
        }

        .message-author {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .message-date {
            font-size: 0.75rem;
            color: #999;
            margin: 0;
            text-align: center;
            width: 100%;
        }

        .message-actions {
            gap: 4px;
        }

        .message-content {
            margin-top: 4px;
            padding-left: 0 !important;
            text-indent: 2em !important;
        }

        /* 更小屏幕的按钮优化 */
        .edit-btn,
        .delete-btn {
            padding: 4px 6px;
            font-size: 0.75rem;
            min-width: 32px;
            height: 28px;
        }

        .collapse-btn {
            width: 32px;
            height: 32px;
            padding: 4px;
            font-size: 0.8rem;
        }

        .action-btn {
            padding: 6px 12px;
            font-size: 0.8rem;
            min-width: 40px;
            height: 36px;
        }

        .group-header {
            padding: 8px 12px;
            border-radius: 12px;
        }

        .group-title {
            font-size: 0.9rem;
            gap: 8px;
        }

        .delete-group-btn {
            padding: 3px 4px;
            font-size: 0.7rem;
            min-width: 24px;
            height: 24px;
        }

        /* 小屏幕月份分组样式 */
        .month-group {
            margin-left: 8px;
        }

        .month-header {
            padding: 8px 10px;
            border-radius: 10px;
        }

        .month-title {
            font-size: 0.85rem;
            gap: 6px;
        }

        .month-main-title {
            font-size: 1rem;
        }

        .month-heart {
            font-size: 0.8rem;
        }

        .month-message-count {
            font-size: 0.75rem;
            padding: 1px 4px;
        }

        .delete-month-btn {
            padding: 2px 4px;
            font-size: 0.7rem;
            min-width: 20px;
            height: 20px;
        }

        .month-content .message-card {
            margin-left: 8px;
        }
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .title {
        font-size: 2.5rem;
    }

    .counter-display {
        gap: 15px;
    }

    .counter-number {
        font-size: 2.8rem; /* 从2.2rem调大到2.8rem */
    }

    .birthday-date {
        font-size: 1.8rem;
    }

    .countdown-days {
        font-size: 1.3rem;
    }

    .start-date {
        font-size: 1rem;
    }

    .birthday-cards {
        grid-template-columns: 1fr;
    }

    .memory-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

/* Memory Modal Styles */
.memory-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.memory-modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

.memory-modal .modal-content {
    background: white;
    border-radius: 20px;
    padding: 0;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes romanticModalAppear {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8) translateY(-30px);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1) translateY(0);
    }
}

.memory-modal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: 20px 20px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.memory-modal .modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.memory-modal .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    font-family: 'Inter', 'ZiXiaoHunGouYu', sans-serif;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.memory-modal .close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.memory-modal .modal-body {
    padding: 30px;
}

.memory-detail {
    text-align: center;
}

.memory-detail .memory-icon {
    font-size: 4rem;
    margin-bottom: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.memory-detail h4 {
    font-size: 1.8rem;
    margin-bottom: 15px;
    color: #333;
}

.memory-detail .memory-date {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 20px;
    font-style: italic;
}

.memory-detail .memory-description {
    font-size: 1rem;
    line-height: 1.6;
    color: #555;
    margin-bottom: 20px;
}

.memory-detail .memory-quote {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    font-style: italic;
    font-size: 1.1rem;
    margin-top: 20px;
}

/* Memory item hover effect */
.memory-item {
    cursor: pointer;
    transition: all 0.3s ease;
}

.memory-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* 隐藏模态框滚动条 */
.modal-content {
    /* 隐藏滚动条但保持滚动功能 */
    scrollbar-width: none !important; /* Firefox */
    -ms-overflow-style: none !important; /* IE and Edge */
}

.modal-content::-webkit-scrollbar {
    display: none !important; /* Chrome, Safari, Opera */
}
