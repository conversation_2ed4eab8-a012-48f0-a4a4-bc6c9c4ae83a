<!DOCTYPE html>
<html lang="zh-CN" style="background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important; margin: 0; padding: 0;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="记录我们爱情路上的重要纪念日和里程碑时刻">
    <title>纪念日 - Yu 💕 Wang</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💕</text></svg>">

    <!-- 立即应用背景样式，防止白色闪烁 -->
    <style>
        html {
            background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
        }
        body {
            background: transparent !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
        }
    </style>

    <link rel="stylesheet" href="/src/client/styles/pages.css">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 绿荫视频背景样式 */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0; /* 初始透明，加载完成后显示 */
            filter: contrast(1.1) saturate(1.1); /* 保持原始亮度，仅增强对比度和饱和度 */
            transition: opacity 1s ease-in-out; /* 平滑过渡效果 */
        }

        /* 视频加载完成后显示 */
        .video-background video.loaded {
            opacity: 1.0; /* 完全不透明，显示视频原始亮度 */
        }

        /* 视频加载时的备用背景 */
        .video-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                #0c0c2e 0%,
                #1a1a3e 25%,
                #2d1b69 50%,
                #4a148c 75%,
                #6a1b9a 100%);
            z-index: -1;
            transition: opacity 1s ease-in-out;
        }

        /* 视频加载完成后隐藏备用背景 */
        .video-background.video-loaded::before {
            opacity: 0;
        }

        /* 加载遮罩样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                #22c55e 0%,
                #16a34a 25%,
                #15803d 50%,
                #166534 75%,
                #14532d 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        /* 加载动画 */
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: white;
            font-family: 'Dancing Script', cursive;
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 10px;
        }

        .loading-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-family: 'Inter', sans-serif;
            font-size: 1rem;
            text-align: center;
        }

        /* 加载进度条 */
        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin-top: 20px;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
            animation: progressPulse 2s ease-in-out infinite;
        }

        @keyframes progressPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 覆盖原有的body背景，让视频背景显示 */
        body {
            background: transparent !important; /* 移除遮罩，让视频背景完全显示 */
        }

        /* 调整星空效果的透明度，与绿荫背景更好融合 */
        body::before {
            opacity: 0.2;
        }

        body::after {
            opacity: 0.15;
        }

        /* 增强内容区域的可读性 */
        .content-section {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-header {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
        }

        .anniversary-counter {
            text-align: center;
            background: transparent; /* 完全透明，让花朵背景完全显示 */
            padding: 30px;
            border-radius: 15px;
            margin: 20px 0;
        }
        
        .counter-display {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .counter-item {
            background: linear-gradient(135deg,
                rgba(255, 182, 193, 0.9) 0%,
                rgba(255, 192, 203, 0.8) 25%,
                rgba(255, 218, 185, 0.9) 50%,
                rgba(255, 228, 225, 0.8) 75%,
                rgba(255, 240, 245, 0.9) 100%);
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(255, 105, 180, 0.3),
                        0 0 20px rgba(255, 182, 193, 0.2);
            /* 移除磨砂效果，让背景完全透明 */
            border: 1px solid rgba(255, 255, 255, 0.3);
            min-width: 80px;
            position: relative;
            overflow: hidden;
            animation: cardGlow 4s ease-in-out infinite;
        }


        
        .counter-number {
            display: block;
            font-family: 'Playfair Display', serif;
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .counter-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }
        
        .milestone-card {
            background: linear-gradient(135deg,
                rgba(255, 182, 193, 0.9) 0%,
                rgba(255, 192, 203, 0.8) 25%,
                rgba(255, 218, 185, 0.9) 50%,
                rgba(255, 228, 225, 0.8) 75%,
                rgba(255, 240, 245, 0.9) 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            box-shadow: 0 15px 35px rgba(255, 105, 180, 0.3),
                        0 0 25px rgba(255, 182, 193, 0.2);
            /* 移除磨砂效果，让背景完全透明 */
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-left: 5px solid;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            animation: cardGlow 5s ease-in-out infinite;
        }


        
        .milestone-card:hover {
            transform: translateY(-3px);
        }
        
        .milestone-card:nth-child(odd) {
            border-left-color: #667eea;
        }
        
        .milestone-card:nth-child(even) {
            border-left-color: #f093fb;
        }
        
        .milestone-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .milestone-icon {
            font-size: 2rem;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .milestone-date {
            font-family: 'Dancing Script', cursive;
            font-size: 1.3rem;
            color: #667eea;
            font-weight: 600;
        }
        
        .milestone-title {
            font-family: 'Playfair Display', serif;
            font-size: 1.4rem;
            font-weight: 600;
            color: #333;
            margin: 5px 0;
        }
        
        .milestone-description {
            color: #666;
            line-height: 1.6;
        }
        
        .special-date {
            background: transparent; /* 完全透明，让花朵背景完全显示 */
            border: 2px solid rgba(240, 147, 251, 0.3);
        }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">🌿 加载绿荫背景中...</div>
        <div class="loading-subtitle">正在为您准备美好的纪念日回忆</div>
        <div class="loading-progress">
            <div class="loading-progress-bar" id="loadingProgress"></div>
        </div>
    </div>

    <!-- 绿荫视频背景 -->
    <div class="video-background">
        <video autoplay muted loop playsinline preload="metadata">
            <source src="/src/client/assets/video-compressed/anniversary.mp4" type="video/mp4">
            <!-- 如果浏览器不支持视频，显示备用背景 -->
            您的浏览器不支持视频播放。
        </video>
    </div>

    <!-- 流星效果 -->
    <div class="shooting-stars">
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
    </div>

    <!-- 浪漫漂浮元素 -->
    <div class="hearts-container">
        <div class="heart type-1 small"></div>
        <div class="heart type-2 medium"></div>
        <div class="heart type-3 large"></div>
        <div class="heart type-4 small"></div>
        <div class="heart type-5 medium"></div>
        <div class="heart type-6 large"></div>
        <div class="heart type-7 small"></div>
        <div class="heart type-8 medium"></div>
        <div class="heart type-9 large"></div>
        <div class="heart type-10 small"></div>
        <div class="heart type-11 medium"></div>
        <div class="heart type-12 large"></div>
        <div class="heart type-1 medium"></div>
        <div class="heart type-3 small"></div>
        <div class="heart type-5 large"></div>
    </div>

    <div class="container">
        <!-- 返回按钮 -->
        <a href="javascript:void(0)" onclick="window.location.href = getHomeUrl()" class="back-button">
            <i class="fas fa-arrow-left"></i>
            返回主页
        </a>

        <!-- 页面头部 -->
        <header class="page-header">
            <i class="fas fa-star page-icon"></i>
            <h1 class="page-title">纪念日</h1>
            <p class="page-subtitle">记录我们爱情路上的每一个重要时刻</p>
        </header>

        <!-- 恋爱天数计数器 -->
        <section class="content-section">
            <h2 class="section-title">我们在一起的时间</h2>
            <div class="anniversary-counter">
                <p style="font-family: 'Dancing Script', cursive; font-size: 1.3rem; color: #667eea; margin-bottom: 15px;">
                    从 2023年4月23日 开始
                </p>
                <div class="counter-display">
                    <div class="counter-item">
                        <span class="counter-number" id="days">0</span>
                        <span class="counter-label">天</span>
                    </div>
                    <div class="counter-item">
                        <span class="counter-number" id="hours">0</span>
                        <span class="counter-label">小时</span>
                    </div>
                    <div class="counter-item">
                        <span class="counter-number" id="minutes">0</span>
                        <span class="counter-label">分钟</span>
                    </div>
                    <div class="counter-item">
                        <span class="counter-number" id="seconds">0</span>
                        <span class="counter-label">秒</span>
                    </div>
                </div>
                <p style="font-family: 'Dancing Script', cursive; font-size: 1.1rem; color: #888;">
                    每一秒都是爱情的见证 💕
                </p>
            </div>
        </section>

        <!-- 重要纪念日 -->
        <section class="content-section">
            <h2 class="section-title">我们的重要纪念日</h2>
            
            <div class="milestone-card special-date">
                <div class="milestone-header">
                    <div class="milestone-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <div>
                        <div class="milestone-date">2023年4月23日</div>
                        <h3 class="milestone-title">💕 确定关系纪念日</h3>
                    </div>
                </div>
                <p class="milestone-description">
                    这是我们爱情故事的开始，从这一天起，我们正式成为了恋人。
                    那一刻的心动和甜蜜，至今还深深印在心里。这是我们最重要的纪念日。
                </p>
            </div>

            <div class="milestone-card">
                <div class="milestone-header">
                    <div class="milestone-icon">
                        <i class="fas fa-gift"></i>
                    </div>
                    <div>
                        <div class="milestone-date">2023年5月14日</div>
                        <h3 class="milestone-title">🎁 第一份礼物纪念日</h3>
                    </div>
                </div>
                <p class="milestone-description">
                    你送给我的第一份礼物，那条精美的项链。虽然不是很贵重，
                    但那份心意比什么都珍贵。从那天起，我就知道你是真心爱我的。
                </p>
            </div>

            <div class="milestone-card">
                <div class="milestone-header">
                    <div class="milestone-icon">
                        <i class="fas fa-plane"></i>
                    </div>
                    <div>
                        <div class="milestone-date">2023年8月20日</div>
                        <h3 class="milestone-title">✈️ 第一次旅行纪念日</h3>
                    </div>
                </div>
                <p class="milestone-description">
                    我们的第一次旅行，去了海边看日出。那是我见过最美的日出，
                    不仅因为风景，更因为有你在身边。这次旅行让我们的感情更加深厚。
                </p>
            </div>

            <div class="milestone-card">
                <div class="milestone-header">
                    <div class="milestone-icon">
                        <i class="fas fa-home"></i>
                    </div>
                    <div>
                        <div class="milestone-date">2023年12月25日</div>
                        <h3 class="milestone-title">🏠 第一个圣诞节</h3>
                    </div>
                </div>
                <p class="milestone-description">
                    我们一起度过的第一个圣诞节，你精心准备了圣诞礼物和惊喜。
                    那个温馨的夜晚，让我感受到了家的温暖和爱的力量。
                </p>
            </div>

            <div class="milestone-card">
                <div class="milestone-header">
                    <div class="milestone-icon">
                        <i class="fas fa-fireworks"></i>
                    </div>
                    <div>
                        <div class="milestone-date">2024年1月1日</div>
                        <h3 class="milestone-title">🎊 第一个新年</h3>
                    </div>
                </div>
                <p class="milestone-description">
                    新年的钟声响起时，我们紧紧拥抱在一起。那一刻许下的愿望，
                    希望我们的爱情能够长长久久，希望每一年都能和你一起度过。
                </p>
            </div>
        </section>

        <!-- 月度纪念日 -->
        <section class="content-section">
            <h2 class="section-title">每月纪念日</h2>
            <div class="section-content">
                <p style="font-family: 'Dancing Script', cursive; font-size: 1.3rem; color: #667eea; margin-bottom: 20px;">
                    每个月的23号，都是我们的小纪念日 💕
                </p>
                <p>
                    从确定关系的那一天起，每个月的23号都变得特别有意义。
                    这一天，我们会回忆起初相遇时的美好，感受彼此的爱意，
                    也会为未来的日子许下新的愿望。
                </p>
                <br>
                <p>
                    有时候是一顿浪漫的晚餐，有时候是一次简单的散步，
                    有时候只是一个温暖的拥抱。不管以什么方式庆祝，
                    重要的是我们在一起，重要的是这份爱依然如初。
                </p>
            </div>
        </section>

        <!-- 爱情感悟 -->
        <section class="quote-section">
            <p class="quote-text">
                "时间是爱情最好的见证者，每一个纪念日都是我们爱情的里程碑。
                愿我们能够一起创造更多值得纪念的美好时刻。"
            </p>
            <p class="quote-author">— 我们的纪念日感悟</p>
        </section>

        <!-- 未来纪念日展望 -->
        <section class="content-section">
            <h2 class="section-title">未来的纪念日</h2>
            <div class="memory-grid">
                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-ring"></i>
                    </div>
                    <h3 class="memory-card-title">求婚纪念日</h3>
                    <p class="memory-card-content">
                        期待着那个特殊的日子，当你单膝跪地的那一刻，
                        将会成为我们最浪漫的纪念日。
                    </p>
                </div>

                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-church"></i>
                    </div>
                    <h3 class="memory-card-title">结婚纪念日</h3>
                    <p class="memory-card-content">
                        那将是我们人生中最重要的一天，从恋人变成夫妻，
                        开始我们的新生活。
                    </p>
                </div>

                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-baby"></i>
                    </div>
                    <h3 class="memory-card-title">宝宝诞生日</h3>
                    <p class="memory-card-content">
                        期待着我们爱情结晶的到来，那将是我们生命中
                        最珍贵的礼物和最美好的纪念日。
                    </p>
                </div>

                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-heart-circle-check"></i>
                    </div>
                    <h3 class="memory-card-title">金婚纪念日</h3>
                    <p class="memory-card-content">
                        50年后的我们，依然手牵手走过人生的每一天，
                        那将是我们爱情最完美的见证。
                    </p>
                </div>
            </div>
        </section>
    </div>

    <!-- 引入前端配置 -->
    <script src="../scripts/config.js"></script>

    <!-- 智能视频加载器 - 四层CDN架构 -->
    <script src="../scripts/video-loader.js"></script>

    <script>
        // 恋爱天数计算器
        function updateCounter() {
            const startDate = new Date('2023-04-23T00:00:00');
            const now = new Date();
            const diff = now - startDate;

            const days = Math.floor(diff / (1000 * 60 * 60 * 24));
            const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((diff % (1000 * 60)) / 1000);

            document.getElementById('days').textContent = days;
            document.getElementById('hours').textContent = hours;
            document.getElementById('minutes').textContent = minutes;
            document.getElementById('seconds').textContent = seconds;
        }

        // 每秒更新一次
        updateCounter();
        setInterval(updateCounter, 1000);

        // 智能视频加载器集成 - 四层CDN架构
        const USE_SMART_LOADER = true; // 新旧系统切换开关

        const video = document.querySelector('.video-background video');
        const videoContainer = document.querySelector('.video-background');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingProgress = document.getElementById('loadingProgress');

        // 视频加载状态标记 (保持兼容性)
        let videoInitialized = false;
        let videoLoadingStarted = false;

        // 隐藏加载遮罩 (保持原有函数)
        function hideLoadingOverlay() {
            if (loadingProgress) loadingProgress.style.width = '100%';
            setTimeout(function() {
                if (loadingOverlay) loadingOverlay.classList.add('hidden');
            }, 300);
        }

        if (USE_SMART_LOADER && typeof VideoLoader !== 'undefined') {
            // 使用四层智能加载器
            console.log('🎬 启用四层智能视频加载器 - 纪念日');

            VideoLoader.integrateWithPage({
                pageName: 'anniversary',
                videoSelector: '.video-background video',
                loadingOverlaySelector: '#loadingOverlay',
                loadingProgressSelector: '#loadingProgress'
            });

        } else if (video) {
            // 降级到原有加载逻辑 (保持完整兼容性)
            console.log('📼 使用原有视频加载逻辑 - 纪念日');
            loadVideoWithOriginalMethod();
        } else {
            // 如果没有视频元素，直接隐藏加载遮罩
            hideLoadingOverlay();
        }

        // 原有加载逻辑封装 (作为降级方案)
        function loadVideoWithOriginalMethod() {
            let progressInterval;
            let currentProgress = 0;

            // 模拟加载进度
            function updateProgress() {
                if (currentProgress < 90) {
                    currentProgress += Math.random() * 15;
                    if (currentProgress > 90) currentProgress = 90;
                    if (loadingProgress) loadingProgress.style.width = currentProgress + '%';
                }
            }

            // 开始进度模拟
            progressInterval = setInterval(updateProgress, 200);

            // 视频开始加载
            video.addEventListener('loadstart', function() {
                if (!videoLoadingStarted) {
                    console.log('绿荫视频开始加载...');
                    videoLoadingStarted = true;
                }
            });

            // 视频有足够数据可以播放
            video.addEventListener('canplay', function() {
                if (!videoInitialized) {
                    console.log('绿荫视频可以播放');
                    clearInterval(progressInterval);
                    currentProgress = 95;
                    if (loadingProgress) loadingProgress.style.width = currentProgress + '%';

                    // 确保视频自动播放
                    video.play().catch(function(error) {
                        console.log('视频自动播放被阻止:', error);
                        hideLoadingOverlay();
                    });
                }
            });

            // 视频加载完成并开始播放
            video.addEventListener('playing', function() {
                if (!videoInitialized) {
                    console.log('绿荫视频背景加载成功并开始播放');
                    clearInterval(progressInterval);
                    video.classList.add('loaded');
                    if (videoContainer) videoContainer.classList.add('video-loaded');
                    hideLoadingOverlay();
                    videoInitialized = true;
                }
            });

            // 视频加载失败
            video.addEventListener('error', function() {
                if (!videoInitialized) {
                    console.log('绿荫视频背景加载失败，使用绿色主题备用背景');
                    clearInterval(progressInterval);
                    video.style.display = 'none';
                    if (videoContainer) {
                        videoContainer.style.background = 'linear-gradient(135deg, #22c55e 0%, #16a34a 25%, #15803d 50%, #166534 75%, #14532d 100%)';
                        videoContainer.classList.add('video-loaded');
                    }
                    hideLoadingOverlay();
                    videoInitialized = true;
                }
            });

            // 设置超时
            setTimeout(function() {
                if (!video.classList.contains('loaded')) {
                    console.log('视频加载超时，切换到绿色主题背景');
                    clearInterval(progressInterval);
                    if (videoContainer) {
                        videoContainer.style.background = 'linear-gradient(135deg, #22c55e 0%, #16a34a 25%, #15803d 50%, #166534 75%, #14532d 100%)';
                        videoContainer.classList.add('video-loaded');
                    }
                    hideLoadingOverlay();
                }
            }, 10000);
        }

        // 页面卸载时清理视频资源
        window.addEventListener('beforeunload', function() {
            const video = document.querySelector('.video-background video');
            if (video) {
                video.pause();
                video.src = '';
                video.load();
                console.log('🧹 页面卸载，已清理视频资源');
            }
        });

        // 页面隐藏时暂停视频，显示时恢复播放
        document.addEventListener('visibilitychange', function() {
            const video = document.querySelector('.video-background video');
            if (video && videoInitialized) {
                if (document.hidden) {
                    video.pause();
                    console.log('📱 页面隐藏，暂停视频播放');
                } else {
                    video.play().catch(function(error) {
                        console.log('📱 页面显示，恢复视频播放失败:', error);
                    });
                    console.log('📱 页面显示，恢复视频播放');
                }
            }
        });
    </script>
</body>
</html>
