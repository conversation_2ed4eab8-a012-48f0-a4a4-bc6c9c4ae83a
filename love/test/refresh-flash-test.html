<!DOCTYPE html>
<html lang="zh-CN" style="background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important; margin: 0; padding: 0;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>刷新白色闪烁测试 - Yu 💕 Wang</title>
    
    <!-- 立即应用背景样式，防止白色闪烁 -->
    <style>
        html {
            background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
        }
        body {
            background: transparent !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
            font-family: 'Arial', sans-serif;
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 600px;
            margin: 20px;
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #ff6b9d, #67e8f9);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .test-instructions {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .test-button {
            background: linear-gradient(45deg, #ff6b9d, #67e8f9);
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            color: white;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.3s ease;
            margin: 10px;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 10px;
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid rgba(0, 255, 0, 0.3);
        }
        
        .navigation {
            margin-top: 30px;
        }
        
        .nav-link {
            color: #67e8f9;
            text-decoration: none;
            margin: 0 15px;
            font-size: 1.1rem;
            transition: color 0.3s ease;
        }
        
        .nav-link:hover {
            color: #ff6b9d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 刷新白色闪烁测试</h1>
        
        <div class="test-instructions">
            <p><strong>测试说明：</strong></p>
            <p>1. 按 <kbd>F5</kbd> 刷新页面，观察是否还有白色闪烁</p>
            <p>2. 按 <kbd>Ctrl+F5</kbd> 强制刷新，观察效果</p>
            <p>3. 点击下方按钮进行自动刷新测试</p>
        </div>
        
        <button class="test-button" onclick="location.reload()">
            🔄 普通刷新 (F5)
        </button>
        
        <button class="test-button" onclick="location.reload(true)">
            🔄 强制刷新 (Ctrl+F5)
        </button>
        
        <div class="status">
            ✅ <strong>修复状态：</strong> 已应用防白色闪烁修复<br>
            📝 <strong>修复方法：</strong> html标签内联样式 + head中立即样式 + CSS文件html样式
        </div>
        
        <div class="navigation">
            <a href="/src/client/pages/index.html" class="nav-link">🏠 返回首页</a>
            <a href="/src/client/pages/anniversary.html" class="nav-link">💕 纪念日</a>
            <a href="/src/client/pages/meetings.html" class="nav-link">👫 相遇</a>
        </div>
    </div>
    
    <script>
        // 页面加载完成时显示状态
        window.addEventListener('load', function() {
            console.log('✅ 页面加载完成，无白色闪烁');
        });
        
        // 监听页面刷新前的状态
        window.addEventListener('beforeunload', function() {
            console.log('🔄 页面即将刷新...');
        });
    </script>
</body>
</html>
